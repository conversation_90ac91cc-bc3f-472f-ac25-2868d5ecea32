{
  "extends": "@pro-ui/typescript-config/tsconfig.dom.json",
  "compilerOptions": {
    "noImplicitAny": false,
    "composite": true,
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"],
      "d3": ["./node_modules/d3"],
      "d3v7": ["./node_modules/d3v7"]
    },
    "types": ["jquery"]
  },
  "include": ["env.d.ts", "src/**/*", "src/**/*.tsx", "src/**/*.vue"],
  "exclude": ["src/**/*/__tests__/*"],
}
