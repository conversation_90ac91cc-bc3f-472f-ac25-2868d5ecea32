import { isNumber } from 'lodash';

import { cache, type HttpClient } from '@/utils/http-client';
import { transformCompanyImageUrl, transformCompanyRegistcapi } from '@/utils/company';

export const BLACKLIST_BASE = '/blacklist';
export const BLACKLIST_CREATE = `${BLACKLIST_BASE}/inner`;
export const BLACKLIST_SEARCH_INNER = `${BLACKLIST_BASE}/inner/search`;
export const BLACKLIST_FILTER_SEARCH = `${BLACKLIST_BASE}/inner/search/aggs`;
export const BLACKLIST_DELETE_INNER = `${BLACKLIST_BASE}/inner/remove`;
export const BLACKLIST_UPDATE_GROUP = `${BLACKLIST_BASE}/inner/change/batch`;
export const BLACKLIST_UPDATE_LABELS = `${BLACKLIST_BASE}/inner/label/batch`;
export const BLACKLIST_SEARCH_OUTER = '/user_configuration/outerblacklist';
export const BLACKLIST_SEARCH_COMPANY = '/company/ssearchForQcc';
export const INDUSTRY = '/schemas/industryKV';
export const BLACKLIST_IMPORT = '/batch/import/blacklist/inner/excel';
export const BLACKLIST_STATUS = `${BLACKLIST_CREATE}/registration/status`;
export const BLACKLIST_TRANSFER = `${BLACKLIST_CREATE}/fromCustomer`;

export const createService = (httpClient: HttpClient) => ({
  async search(data): Promise<any> {
    const res = await httpClient.post(BLACKLIST_SEARCH_INNER, data);
    return transformCompanyImageUrl(transformCompanyRegistcapi(res));
  },
  aggsSearch(data): Promise<any> {
    return httpClient.post(BLACKLIST_FILTER_SEARCH, data);
  },
  searchOuter(): Promise<any> {
    return httpClient.get(BLACKLIST_SEARCH_OUTER);
  },
  updateStatus(data): Promise<any> {
    return httpClient.post(BLACKLIST_SEARCH_OUTER, data);
  },
  create(data): Promise<any> {
    return httpClient.post(BLACKLIST_CREATE, data);
  },
  delete(data): Promise<any> {
    return httpClient.post(BLACKLIST_DELETE_INNER, data);
  },
  edit(data, id): Promise<any> {
    return httpClient.post(`/blacklist/inner/details/${id}`, data);
  },
  searchCompany(data): Promise<any> {
    return httpClient.post(BLACKLIST_SEARCH_COMPANY, { data, isHighlight: true });
  },
  searchIndustry(): Promise<any> {
    return cache(() => httpClient.get(INDUSTRY), INDUSTRY);
  },
  import(data): Promise<any> {
    const file = data.get('file');
    if (!file) return Promise.resolve('请选择文件');
    return httpClient.post(`${BLACKLIST_IMPORT}?fileName=${file.name}`, data);
  },
  export(data): Promise<any> {
    return httpClient.post('/batch/export/inner_blacklist', data);
  },
  async searchEconKinds() {
    return httpClient.post(`/blacklist/inner/econKind/list`);
  },

  async getCount() {
    const res = await httpClient.post(`/blacklist/inner/count`);
    return isNumber(res) ? res : res?.data;
  },

  async getBlackListDepts() {
    return httpClient.post(`/blacklist/inner/aggregation?key=department`);
  },
  updateGroup(data): Promise<any> {
    return httpClient.post(BLACKLIST_UPDATE_GROUP, data);
  },
  updateLabels(data): Promise<any> {
    return httpClient.post(BLACKLIST_UPDATE_LABELS, data);
  },
  getStatus(): Promise<any> {
    return httpClient.post(BLACKLIST_STATUS);
  },
  /*
   *
   *通过companyIds匹配在第三方列表中的公司列表
   */
  transParter2Black(data) {
    return httpClient.post(BLACKLIST_TRANSFER, data);
  },
  validateCount(data) {
    return httpClient.post('/blacklist/inner/validate-count', data);
  },
});
