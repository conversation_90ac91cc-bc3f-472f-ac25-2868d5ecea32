import { isNumber } from 'lodash';

import { transformCompanyImageUrl, transformCompanyRegistcapi } from '@/utils/company';
import type { HttpClient } from '@/utils/http-client';

export const CUSTOMER_BASE = '/customers';
export const CUSTOMER_CREATE = `${CUSTOMER_BASE}`;
export const CUSTOMER_SEARCH = `${CUSTOMER_BASE}/search`;
export const CUSTOMER_DELETE = `${CUSTOMER_BASE}/remove`;
export const CUSTOMER_UPDATE_GROUP = `${CUSTOMER_BASE}/change/batch`;
export const CUSTOMER_IMPORT = '/batch/import/customer/excel';
export const CUSTOMER_BATCH_LABELS = `${CUSTOMER_BASE}/label/batch`;
export const CUSTOMER_BATCH_COMPANY = `${CUSTOMER_BASE}/match`;

export const createService = (httpClient: HttpClient) => ({
  async search(data, skipLogo = false): Promise<any> {
    const res = await httpClient.post(CUSTOMER_SEARCH, data);
    const resData = transformCompanyRegistcapi(res);
    return skipLogo ? resData : transformCompanyImageUrl(resData);
  },
  chart() {
    return httpClient.post(`/customers/search/chart`);
  },
  create(data): Promise<any> {
    return httpClient.post(CUSTOMER_CREATE, data);
  },
  edit(data, id): Promise<any> {
    return httpClient.post(`/customers/details/${id}`, data);
  },
  delete(data): Promise<any> {
    return httpClient.post(CUSTOMER_DELETE, data);
  },
  updateGroup(data): Promise<any> {
    return httpClient.post(CUSTOMER_UPDATE_GROUP, data);
  },
  import(data): Promise<any> {
    const file = data.get('file');
    if (!file) return Promise.resolve('请选择文件');
    return httpClient.post(`${CUSTOMER_IMPORT}?fileName=${file.name}`, data);
  },
  batchLabels(data): Promise<any> {
    return httpClient.post(CUSTOMER_BATCH_LABELS, data);
  },
  getRiskCharts(data): Promise<any> {
    return httpClient.post(`/charts/risk/v2`, data);
  },
  async getRiskDetail(params) {
    return httpClient.post('/charts/risk/v2/detail', params);
  },
  getRiskChartsHistory(): Promise<any[]> {
    return httpClient.get(`/charts/history`);
  },
  updateRiskCharts(params): Promise<any> {
    return httpClient.get(`/charts/analyze_customer/sync`, {
      params,
    });
  },

  export(data): Promise<any> {
    return httpClient.post(`/batch/export/customer`, data);
  },
  async searchEconKinds() {
    return httpClient.post(`/customers/econKind/list`);
  },
  async aggsSearch(data) {
    return httpClient.post('/customers/search/aggs', data);
  },
  async getRegistrationStatus() {
    return httpClient.post('/customers/registration/status');
  },
  async getCount() {
    const res = await httpClient.post('/customers/count');
    return isNumber(res) ? res : res?.data;
  },

  async getMonitorStatus(data) {
    const res = await httpClient.post('/customers/monitor/status', data);
    if (!res.data && res.responseData) {
      res.data = res.responseData.map((item) => {
        return {
          ...item,
          name: item.companyName,
        };
      });
    }
    return res;
  },
  checkInsertThird(id) {
    return httpClient.get(`/customers/check/company?companyId=${id}`);
  },
  /*
   *
   *通过companyIds匹配在第三方列表中的公司列表
   */
  getBacthCompanys(companyIds) {
    return httpClient.post(CUSTOMER_BATCH_COMPANY, { companyIds });
  },

  /*
   *
   *通过id获取信用分
   */
  getCreditRate(id) {
    return httpClient.get(`/customers/${id}/credit-rate`);
  },
  /*
   *
   *通过id获取信用分
   */
  batchCreate(data) {
    return httpClient.post('/customers/batchCreate', data);
  },
  validateCount(data) {
    return httpClient.post('/customers/validate-count', data);
  },
});
