import { computed, defineComponent, onMounted, ref } from 'vue';
import { message as Message } from 'ant-design-vue';
import { cloneDeep } from 'lodash';

import { useRequest } from '@/shared/composables/use-request';
import { setting as settingService } from '@/shared/services';
import QLoading from '@/components/global/q-loading';
import { createTrackEvent, useTrack } from '@/config/tracking-events';
import SaveBeforeLeave from '@/apps/setting/components/save-before-leave';
import UpgradeReminder from '@/apps/setting/components/upgrade-reminder';

import { MonitorRiskLevel, MonitorSettings } from './config/public-opinion-dynamics.config';
import TabsController from '../../widgets/tabs-controller';
import styles from './public-opinion-dynamics.module.less';
import { openSettingUpdateModal } from '../../../investigation-setting/widgets/setting-update-modal';
import MonitorSettingBlock from '@/apps/setting/components/monitor-setting-block';

const PublicOpinionDynamics = defineComponent({
  name: 'PublicOpinionDynamics',
  props: {},
  setup() {
    const track = useTrack();
    const isEdit = ref(false);
    const isUpgrade = ref(false);
    /** 是否禁用 */
    const disabled = computed(() => {
      return !isEdit.value;
    });
    /** 设置是否有变化 */
    const hasSettingChanged = ref(false);
    const handleDraggableUpdate = (data) => {
      hasSettingChanged.value = true;
      monitorSettings.value = data;
      track(createTrackEvent(7729, '合作监控设置', '拖动排序', '舆情动态设置'));
    };

    /** 重置状态 */
    const resetState = () => {
      isEdit.value = false;
      hasSettingChanged.value = false;
    };

    const { execute, status, isIdle, isLoading, isSuccess, data: result } = useRequest<any>(settingService.getMonitorNewsSetting);

    // 能保存的设置
    const monitorSettings = ref<MonitorSettings>({
      2: [],
      1: [],
      0: [],
      NotConcerned: [],
      MonitoringScope: undefined,
      MonitorStatus: undefined,
    });

    /** 获取设置 */
    const fetchSettings = async () => {
      const { content } = await execute();
      monitorSettings.value = cloneDeep(content);
    };

    onMounted(() => {
      fetchSettings();
    });

    /** 更新整体设置 */
    const handleUpdateMonitorSettings = async () => {
      try {
        await settingService.updateMonitorNewsSetting({
          ...result.value,
          content: monitorSettings.value,
        });
        track(createTrackEvent(7729, '合作监控设置', '保存', '舆情动态设置'));
        // reset
        resetState();
        Message.success('保存成功');
      } catch (error) {
        console.error(error);
      }
    };

    const handleOpenUpgradeModal = async () => {
      track(createTrackEvent(7727, '舆情动态设置', '立即查看', '风险动态设置'));
      const data: any = await openSettingUpdateModal({
        title: '合作监控设置更新',
        isCooperative: true,
        id: result.value.id,
        permission: [2088],
      });
      if (data) {
        isEdit.value = true;
        isUpgrade.value = true;
        monitorSettings.value = cloneDeep(data.content);
        result.value.canUpgrade = false;
        result.value.systemSettingsId = data.systemSettingId;
        track(createTrackEvent(7727, '舆情动态设置', '更新到最新版本', '风险动态设置'));
      }
    };

    /** 重置整体设置 */
    const handleResetMonitorSettings = async () => {
      // 会出现查看更新内容然后又取消的情况
      resetState();
      fetchSettings();
    };

    /** 更新设置筛选项 */
    const handleChangeMonitoringScope = (ev) => {
      monitorSettings.value[MonitorRiskLevel.MonitoringScope] = {
        ...monitorSettings.value[MonitorRiskLevel.MonitoringScope],
        status: ev.target?.checked ? 1 : 0,
      };
      hasSettingChanged.value = true;
    };

    /** 更新设置筛选项 */
    const handleChangeMonitoringStatus = (ev) => {
      monitorSettings.value[MonitorRiskLevel.MonitorStatus] = {
        ...monitorSettings.value[MonitorRiskLevel.MonitorStatus],
        name: '舆情监控状态设置',
        status: ev ? 1 : 2,
      };
      hasSettingChanged.value = true;
    };

    return {
      monitorSettings,
      result,
      handleChangeMonitoringScope,
      handleChangeMonitoringStatus,
      handleUpdateMonitorSettings,
      handleResetMonitorSettings,
      fetchSettings,
      status,
      isIdle,
      isLoading,
      isSuccess,

      /** 设置是否有变化 */
      hasSettingChanged,
      handleDraggableUpdate,

      /** 是否禁用 */
      disabled,
      isEdit,
      handleOpenUpgradeModal,
    };
  },
  render() {
    if (this.isIdle || this.isLoading) {
      return (
        <TabsController>
          <div style={{ height: 'calc(100vh - 137px)' }}>
            <QLoading size="fullsize" />
          </div>
        </TabsController>
      );
    }

    if (this.isSuccess) {
      return (
        <TabsController>
          <SaveBeforeLeave
            v-permission={[2090]}
            slot="extra"
            v-model={this.isEdit}
            onSave={this.handleUpdateMonitorSettings}
            onAfterCancel={this.handleResetMonitorSettings}
            trackInfo={{ pageCode: 7729, pageName: '合作监控设置' }}
          />

          <div class={styles.container}>
            <UpgradeReminder text="舆情动态模型" canUpgrade={this.result.canUpgrade} onClick={this.handleOpenUpgradeModal} />
            {/* Section */}
            <MonitorSettingBlock
              dataSource={this.monitorSettings as any}
              disabled={this.disabled}
              onScopeChange={this.handleChangeMonitoringScope}
              onStatusChange={this.handleChangeMonitoringStatus}
              onUpdate={this.handleDraggableUpdate}
            />
          </div>
        </TabsController>
      );
    }

    return null;
  },
});

export default PublicOpinionDynamics;
