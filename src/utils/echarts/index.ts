import ECharts from 'vue-echarts';
import { use, registerMap, registerTheme } from 'echarts/core';
import { CanvasRenderer } from 'echarts/renderers';
import { MapChart } from 'echarts/charts';
import { TitleComponent, TooltipComponent, VisualMapComponent } from 'echarts/components';

import { CHINA_GEO } from '@pro-ui/core';

use([<PERSON>vas<PERSON><PERSON><PERSON>, MapChart, VisualMapComponent, TitleComponent, TooltipComponent]);

// 注册默认主题
registerTheme('', {
  color: ['#62A5E7', '#7ECF52', '#EECB5F', '#9470E5', '#E3935C', '#E06757', '#605EF0', '#45C2CE', '#4DD593', '#ECB556', '#A877D1'],
});

// 注册中国地图
registerMap('china', JSON.stringify(CHINA_GEO));

export default (Vue) => {
  Vue.component('vue-echarts', ECharts);
};
