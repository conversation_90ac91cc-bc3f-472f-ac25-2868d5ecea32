export interface IResponseModule {
  Result: any;
  Paging: {
    TotalRecords: number;
    PageIndex: number;
    PageSize: number;
  };
}

export interface IAppState {
  name: string;
  keyword: string;
  readRecords: object;
}

export interface IUserState {
  profile: Record<string, any> | null;
  bundle: unknown;
  usage: unknown;
  wholePermissions: any[];
  organizations: any[];
  riskDimensionMap: object;
  tenderDimensionMap: object;
}

export interface ICompanyState {
  companyInfo: Record<string, any>;
  riskInfo: Record<string, any>;
  qualifications: Record<string, any>;
  credits: Record<string, any>;
  activeTabKey: string;
  [key: string]: any;
}

export interface IPersonState {
  detail: unknown;
  nav: unknown;
}

export interface IModalSettingState {
  settingInfo: Record<
    string,
    {
      items: Record<string, any>[];
      sort: number;
      status: number;
      name: string;
    }
  > | null;
  loading: boolean;
}

export interface IMessageState {
  messages: any[];
  messageCount: number;
}

export interface IRootState {
  app: IAppState;
  user: IUserState;
  company: ICompanyState;
  person: IPersonState;
  modalSetting: IModalSettingState;
  message: IMessageState;
}

export interface IPermissions {
  name: string;
  permission: number;
}

export interface ITabPermissions {
  title: string;
  permissions: IPermissions[];
}

export interface IPermissionCategory {
  category: string;
  tabs: ITabPermissions[];
  permissions: IPermissions[] | any;
}

export interface IMonitorState {
  groupList: any[];
  recycleCount: number;
  removeFlag: number;
  hasUpdateSetting: boolean;
}

export type CompanyInfo = {
  KeyNo?: string;
  Name?: string;
  [x: string]: any;
};
