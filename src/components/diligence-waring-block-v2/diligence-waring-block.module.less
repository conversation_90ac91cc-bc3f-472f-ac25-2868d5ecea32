@import '@/styles/token.less';

@color-risk-high: #F04040;
@border-color-risk-high: #ffecec;
@color-risk-medium: #fa0;
@border-color-risk-medium: #fff4e0;
@color-risk-low: #00ad65;
@border-color-risk-low: #f8fffc;



.container {
  display: flex;
  align-items: center;
  gap: 30px;

  .divider {
    color: #eee;
    margin: 0 10px;
  }

  .dot {
    position: relative;
    padding-left: 10px;

    &::before {
      content: '';
      position: absolute;
      top: 8.5px;
      left: 0;
      width: 5px;
      height: 5px;
      border-radius: 5px;
      background: #666;
    }
  }

  .risk-block{
    flex: 1;
    min-width: 0;
    display: inline-flex;
    flex-wrap: wrap;
  }

  .label {
    display: flex;
    align-items: center;
    color: #999;
    flex-shrink: 0;
  }

  .safe {
    color: #999;
  }

  .levelIcon {
    display: flex;
    align-items: center;
    margin-left: 5px;
    margin-right: 10px;

    &.failed {
        color: @color-risk-high;
        border-color: @border-color-risk-high;
    }

    &.warning {
        color: @color-risk-medium;
        border-color: @border-color-risk-medium;
    }

    &.pass {
        color: @color-risk-low;
        border-color: @border-color-risk-low;
    }
  }

  .icon {
    font-size: 18px;
    margin-right: 4px;
    margin-top: -2px;
  }

  .link {
    font-weight: 500;
    color: #333;
    white-space: nowrap;
    position: relative;
    padding-right: 21px;

    &::after {
      content: '';
      position: absolute;
      top: 50%;
      right: 10px;
      transform: translateY(-50%);
      width: 1px;
      height: 1em;
      background: #EEE;
    }
  }

  .noDivider {
    padding-right: 0;

    &::after {
      content: '';
      width: 0;
    }
  }

  .anchor {
    cursor: auto;

    &:hover {
      color:#333;
    }
  }

  .anchorHover {
    &:hover {
      color: @qcc-color-blue-500;
    }
  }

  .riskLevel {
    font-size: 16px;
    font-weight: 500;

    &.failed {
      color: @color-risk-high;
    }

    &.warning {
      color: @color-risk-medium;
    }

    &.pass {
      color: @color-risk-low;
    }
  }

  .riskItem {
    display: flex;
    align-items: flex-start;
    line-height: 22px;
  }

  .mb10 {
    margin-bottom: 10px;
  }

  .content {
    display: inline-flex;
    flex-wrap: wrap;
    flex: 1;
    min-width: 0;
  }

  .tooltip {
    font-size: 14px;
    font-weight: normal;
    line-height: 22px;
    color: #666;
    margin-top: 5px;
  }

  .riskNum {
    white-space: nowrap;
    text-align: right;
    width: 2em;
  }
}
