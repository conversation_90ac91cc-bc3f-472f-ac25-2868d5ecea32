import qs from 'querystring';
import { isEmpty } from 'lodash';
import { defineComponent, getCurrentInstance, nextTick, onMounted, ref } from 'vue';
import { message } from 'ant-design-vue';

import QModal from '@/components/global/q-modal';
import QTabs from '@/components/global/q-tabs';
import { customer, company as companyService } from '@/shared/services';
import UserCountStatistics from '@/components/user-count-statistics';
import { createTrackEvent, useTrack } from '@/config/tracking-events';
import { useUserStore } from '@/shared/composables/use-user-store';

import AddPartnerFormWrapper from './widgets/form';
import ModalUpload from '../modal-upload';
import styles from './add-partner-modal.module.less';

const AddPartnerModal = defineComponent({
  name: 'AddPartnerModal',
  props: {
    params: {
      type: Object,
      default: () => ({}),
    },
  },
  setup(props, { emit }) {
    const track = useTrack();
    const form = ref(null);
    const uploadPartner = ref(null);

    const { isSinoPharm } = useUserStore();
    const visible = ref(true);
    const {
      tab = 'single',
      tabs = [
        {
          label: '新增第三方',
          key: 'single',
        },
        {
          label: '批量上传',
          key: 'bulk',
        },
      ],
    } = props.params;
    const currentTab = ref(tab);
    const handleTabChange = async (nextTab) => {
      currentTab.value = nextTab;
    };

    const handleCancel = () => {
      visible.value = false;
    };

    const depts = ref(props.params.departList || []);

    const initDepartList = async () => {
      if (!depts.value || depts.value?.length === 0) {
        depts.value = await companyService.getCompanyDepartList();
      }
    };

    initDepartList();

    const dealFn = (id) => {
      return id ? customer.edit : customer.create;
    };

    // FIXME: code smell
    const onSubmit = async () => {
      return new Promise((resolve, reject) => {
        if (currentTab.value === 'single') {
          track(createTrackEvent(6226, '第三方列表', '新增第三方确认'));
          const partnerFormRef = (form.value as any).$refs.WrappedComponent.form;
          partnerFormRef.validateFields(async (err, values) => {
            if (err) {
              console.log(err);
              reject();
              return;
            }
            if (!values.companyName) {
              message.error('请选择企业');
              reject();
              return;
            }
            const { contactEmail, contactName, contactPhone } = values;
            if (contactEmail || contactName || contactPhone) {
              values.contacts = [
                {
                  ...(props.params.data.contacts?.[0] || {}),
                  name: contactName || null,
                  phone: contactPhone || null,
                  email: contactEmail || null,
                },
              ];
            } else {
              values.contacts = [];
            }
            // ajax
            const params = {
              ...values,
              name: values.companyName,
              companyId: values.companyId || props.params?.data?.id,
              creditQuota: values.creditQuota || null,
              contactQuota: values.contactQuota || null,
              cost: values.cost || null,
              startDate: values.startDate || null,
              endDate: values.endDate || null,
              econkind: values.econkindcode,
              province: values.province || null,
              city: values.areacode ? values.areacode.split(',')[0] : '',
              district: values.areacode ? values.areacode.split(',')[1] : '',
              industry1: values.industry || null,
              groupId: values.groupId,
              ownerId: values.ownerId || null,
              labelIds: values.labelIds && values.labelIds.length ? values.labelIds : undefined,
              // customerDepartment: values.department?.trim() || null,
              departmentNames: values.departmentNames?.length ? values.departmentNames : undefined,
              principal: values.owner || null,
            };
            (values.subind?.split(',') ?? []).forEach((item, index) => {
              const pIndex = `industry${index + 2}`;
              params[pIndex] = item;
            });
            const freshDept = !depts.value.includes(values.department?.trim());
            if (props.params.from === 'riskWatch') {
              const res = await customer.checkInsertThird(params.companyId);
              if (res.orgId) {
                props.params?.onUpdated({ isOld: false, freshDept });
                emit('resolve');
                resolve(null);
                return;
              }
            }
            try {
              await dealFn(values.id)(params, values.id);
            } catch (error) {
              reject(error);
              return;
            }
            handleCancel();
            props.params?.onUpdated?.({ isOld: props.params?.data?.customerId || props.params?.data?.id, freshDept });
            resolve(null);
          });
        }
      });
    };
    const vm = getCurrentInstance()?.proxy as any;
    onMounted(async () => {
      const { data } = props.params;

      await nextTick();

      if (isEmpty(data)) {
        return;
      }

      const partnerFormRef = (vm.form as any).$refs.WrappedComponent?.form;
      const subindList = Array.isArray(data?.subind) ? data?.subind : [data?.industry2, data?.industry3, data?.industry4];
      const labels = (data?.labels ?? []).map((label) => {
        return label.labelId;
      });

      // contacts额外处理
      const contactObj = data?.contacts?.[0] || {};
      partnerFormRef?.setFields({
        id: { value: data?.customerId },
        name: { value: data?.name },
        companyName: { value: data?.name },
        econkind: { value: data?.econkind },
        companyId: { value: data?.companyId },
        areacode: { value: Array.isArray(data?.areacode) ? data.areacode.join(',') : data?.city },
        econkindcode: { value: data?.econkind },
        province: { value: data?.province },
        subind: { value: subindList.join(',') },
        creditQuota: { value: data?.creditQuota },
        contactQuota: { value: data?.contactQuota },
        cost: { value: data?.cost },
        startDate: { value: data?.startDate },
        endDate: { value: data?.endDate },
        groupId: { value: data?.groupId || null },
        owner: { value: data?.principal },
        // department: { value: data?.customerDepartment },
        departmentNames: { value: data?.departments?.map((el) => el.name) },
        labelIds: { value: labels },
        startDateCode: { value: data?.startDateCode },
        // 登记状态
        statusCode: { value: data?.statusCode },
        registcapi: { value: data?.registcapi },
        registcapiamount: { value: data?.registcapiamount },
        industry: { value: data?.industy || data?.industry1 }, // FIXME: industry1 非必要
        industry1: { value: data?.industry1 },
        industry2: { value: data?.industry2 },
        industry3: { value: data?.industry3 },
        industry4: { value: data?.industry4 },
        contactName: { value: contactObj.name },
        contactPhone: { value: contactObj.phone },
        contactEmail: { value: contactObj.email },
      });
    });
    return {
      visible,
      currentTab,
      tabs,
      form,
      depts,
      uploadPartner,
      handleTabChange,
      onSubmit,
      handleCancel,
      isSinoPharm,
    };
  },
  render() {
    return (
      <QModal
        {...{
          props: {
            onOk: this.onSubmit,
            visible: this.visible,
            wrapClassName: styles.container,
            destroyOnClose: true,
            size: this.currentTab === 'bulk' ? 'x-large' : 'medium',
            afterClose: () => this.$emit('resolve'),
            ...(this.currentTab === 'bulk' ? { footer: null } : {}),
          },
          on: {
            cancel: this.handleCancel,
          },
        }}
      >
        <div slot="title" class="flex-between" style="padding-right: 12px;">
          <QTabs size="large" value={this.currentTab} tabs={this.tabs} onChange={this.handleTabChange} />
          <UserCountStatistics dimension={'thirdPartyQuantity'} />
        </div>
        <div>
          {this.currentTab === 'single' && (
            <AddPartnerFormWrapper
              ref="form"
              groups={this.params.groups || []}
              allTags={this.params.allTags || []}
              disable={this.params.data.disable}
              departList={this.depts}
              data={this.params.data}
            />
          )}
          {this.currentTab === 'bulk' && (
            <ModalUpload
              ref="uploadPartner"
              action={(file) =>
                `/rover/batch/import/customer/excel/parse?${qs.stringify({
                  fileName: file.name,
                })}`
              }
              onSuccess={(data) => {
                this.$track(createTrackEvent(6226, '第三方列表', '上传文件成功'));
                this.handleCancel();
                const { href } = this.$router.resolve({
                  path: `${this.isSinoPharm ? 'third-party-partners' : 'partners'}/upload-confirm`,
                  params: {
                    type: this.isSinoPharm ? 'third-party-partners' : 'third-party/partners',
                  },
                  query: {
                    batchId: data.batchId,
                  },
                });
                window.location.href = href;
              }}
            />
          )}
        </div>
      </QModal>
    );
  },
});

export default AddPartnerModal;
