import { Input, message } from 'ant-design-vue';
import { defineComponent, onMounted, PropType, ref } from 'vue';

import QModal from '@/components/global/q-modal';
import { customerGroup } from '@/shared/services';
import QRichTableEmpty from '@/components/global/q-rich-table/components/empty';

import styles from './partner-group-modal.module.less';
import GroupItemWrapper from './widgets/group-item-wrapper';

const PartnerGroupModal = defineComponent({
  name: 'PartnerGroupModal',
  props: {
    params: {
      type: Object as PropType<Record<string, any>>,
      default: () => ({}) as Record<string, any>,
    },
  },
  setup(props) {
    const visible = ref(false);
    const groupList = ref(props.params?.groups || []);
    const keyword = ref(undefined);
    const selectId = ref(undefined);

    // eslint-disable-next-line @typescript-eslint/no-shadow
    const drawCorrelativeKeyword = (keywords, keyword) => {
      const len = keywords.length;
      const keywordArr: any[] = [];
      for (let i = 0; i < len; i++) {
        const html = keywords[i].name.replace(keyword, `<em style="color: #F04040;">${keyword}</em>`);
        const tmpObj = {
          ...keywords[i],
          showName: html,
        };
        keywordArr.push(tmpObj);
      }
      return keywordArr;
    };

    const getData = async () => {
      const params = {
        name: keyword.value,
        groupType: props.params?.groupType || 1,
      };
      const res = await customerGroup.search(params);
      groupList.value = keyword.value ? drawCorrelativeKeyword(res, keyword.value) : res;
    };

    const changeSelect = (value) => {
      selectId.value = value.groupId;
    };
    const onCancel = () => {
      selectId.value = undefined;
      visible.value = false;
    };
    const onSubmit = async () => {
      try {
        if (selectId.value) {
          await props.params?.updateGroup?.(selectId.value);
          onCancel();
        } else {
          message.warning('请选择一个分组');
        }
      } catch (err: any) {
        message.error(err.message);
      }
    };
    onMounted(() => {
      visible.value = true;
    });
    return {
      visible,
      groupList,
      keyword,
      selectId,
      getData,
      changeSelect,
      onSubmit,
      onCancel,
    };
  },
  render() {
    return (
      <QModal
        title="移动分组至"
        visible={this.visible}
        okButtonProps={{ props: { disabled: !this.groupList.length } }}
        onOk={this.onSubmit}
        onCancel={this.onCancel}
        footer={undefined}
      >
        <div class={styles.container}>
          <header class={styles.header}>
            <Input.Search
              allowClear
              v-model={this.keyword}
              onSearch={this.getData}
              onChange={this.getData}
              placeholder="请输入分组名称"
              suffix={null}
            />
          </header>
          <div class={styles.groups}>
            {this.groupList.length ? (
              <div style="border: 1px solid #eee;">
                {this.groupList.map((groupItem: any) => {
                  return (
                    <GroupItemWrapper
                      key={`${groupItem.groupId}`}
                      group={groupItem}
                      groupList={this.groupList}
                      listMode={'select'}
                      selectId={this.selectId}
                      onChange={this.changeSelect}
                    />
                  );
                })}
              </div>
            ) : (
              <QRichTableEmpty size={'100px'} minHeight={'250px'}>
                <span class={styles.empty}>
                  <div>暂无分组，请新增企业分组</div>
                </span>
              </QRichTableEmpty>
            )}
          </div>
        </div>
        <template slot="footer">
          <a-button onClick={this.onCancel}>取消</a-button>
          <a-button type="primary" onClick={this.onSubmit} disabled={!this.groupList.length}>
            确定
          </a-button>
        </template>
      </QModal>
    );
  },
});

export default PartnerGroupModal;
