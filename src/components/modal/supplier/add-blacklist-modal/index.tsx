import qs from 'querystring';
import { isEmpty, omit } from 'lodash';
import { defineComponent, nextTick, onMounted, ref, unref } from 'vue';
import { message } from 'ant-design-vue';

import QModal from '@/components/global/q-modal';
import { blackList as blackListService } from '@/shared/services';
import QTabs from '@/components/global/q-tabs';
import UserCountStatistics from '@/components/user-count-statistics';
import { createTrackEvent, useTrack } from '@/config/tracking-events';
import { IMPORT_BLACKLIST_TEMPLATE_URL } from '@/config/template.config';

import ModalUpload from '../modal-upload';
import AddBlacklistForm from './widgets/add-blacklist-modal-form';
import styles from './add-blacklist-modal.module.less';

const AddBlackListModal = defineComponent({
  name: 'AddBlackListModal',
  props: {
    params: {
      type: Object,
      default: () => ({}),
    },
  },
  setup(props, { emit }) {
    const track = useTrack();
    const form = ref(null);
    const currentTab = ref(props.params.currentTab || 'single');
    const uploadBlackList = ref(null);
    const visible = ref(true);
    const onCancel = () => {
      visible.value = false;
    };
    const handleOnOk = (freshDept = false) => {
      props.params?.onOk?.({ freshDept });
    };
    const getTabs = () => {
      if (props.params.tabs) {
        return props.params.tabs;
      }
      if (isEmpty(omit(props.params.data, ['groupId']))) {
        return [
          {
            label: '新增黑名单',
            key: 'single',
          },
          {
            label: '批量上传',
            key: 'bulk',
          },
        ];
      }
      return [{ label: '编辑', key: 'single' }];
    };
    const tabs = getTabs();

    const handleTabChange = async (nextTab) => {
      currentTab.value = nextTab;
    };

    const saveFn = props.params.data.id ? blackListService.edit : blackListService.create;

    const onSubmit = async () => {
      return new Promise((resolve, reject) => {
        if (currentTab.value === 'single') {
          track(createTrackEvent(6232, '内部黑名单', '新增黑名单确认'));
          const blacklistFormRef = (form.value as any).$refs.WrappedComponent.form;
          blacklistFormRef.validateFields(async (err, values) => {
            if (err) {
              console.log(err);
              reject();
              return;
            }
            if (!values.companyName) {
              message.error('请选择企业');
              reject();
              return;
            }
            // ajax
            const params = {
              companyName: values.companyName,
              companyId: values.companyId,
              department: values.department?.trim() || null,
              departmentNames: values.departmentNames?.length ? values.departmentNames : undefined,
              comment: values.note || null,
              reason: values.reason || null,
              duration: values.date,
              econkind: values.econkindcode,
              province: values.province || null,
              city: values.areacode ? values.areacode.split(',')[0] : '',
              district: values.areacode ? values.areacode.split(',')[1] : '',
              industry1: values.industry || null,
              joinDate: values.joinDate || null,
              groupId: values.groupId,
              labelIds: values?.labelIds?.length > 0 ? values.labelIds : undefined,
              expiredDate: values?.expiredDate || null,
            };
            (values.subind?.split(',') ?? []).forEach((item, index) => {
              const pIndex = `industry${index + 2}`;
              params[pIndex] = item;
            });
            if (props.params.from === 'riskWatch') {
              const res = await blackListService.search({ searchKey: values.companyName });
              if (res.data.length > 0) {
                props.params?.onOk?.({ exist: true });
                emit('resolve');
                resolve(null);
                return;
              }
            }
            saveFn(params, values.id)
              .then(() => {
                onCancel();
                handleOnOk();
                emit('resolve');
                resolve(true);
              })
              .catch(reject);
          });
        }
      });
    };

    const init = async () => {
      const { data: formData } = props.params;
      await nextTick();
      if (!isEmpty(formData)) {
        const blacklistFormRef = (unref(form) as any).$refs.WrappedComponent.form;
        const subindList = [formData?.industry2, formData?.industry3, formData?.industry4];
        blacklistFormRef.setFields({
          id: { value: formData?.id },
          name: { value: formData?.companyName },
          companyName: { value: formData?.companyName },
          econkind: { value: formData?.econkind },
          companyId: { value: formData?.companyId },
          areacode: { value: formData?.city },
          econkindcode: { value: formData?.econkind },
          industry: { value: formData?.industry1 },
          province: { value: formData?.province },
          subind: { value: subindList.join(',') },
          reason: { value: formData?.reason },
          // department: { value: formData?.department },
          departmentNames: { value: formData.departments?.map((el) => el.name) },
          date: { value: formData?.duration },
          joinDate: { value: formData?.joinDate },
          note: { value: formData?.comment },
          groupId: { value: formData?.groupId || -1 },
          labelIds: { value: formData?.labels?.map((item) => item.labelId) },
          expiredDate: { value: formData?.expiredDate },
        });
      }
    };
    onMounted(() => {
      init();
    });
    return {
      form,
      uploadBlackList,
      onSubmit,
      onCancel,
      tabs,
      currentTab,
      handleTabChange,
      visible,
    };
  },
  render() {
    return (
      <QModal
        {...{
          props: {
            onOk: this.onSubmit,
            visible: this.visible,
            wrapClassName: styles.container,
            destroyOnClose: true,
            size: this.currentTab === 'bulk' ? 'x-large' : 'medium',
            ...(this.currentTab === 'bulk' ? { footer: null } : {}),
          },
          on: {
            cancel: () => this.onCancel(),
          },
        }}
      >
        <div slot="title" class="flex-between" style="padding-right: 12px;">
          <QTabs size="large" value={this.currentTab} tabs={this.tabs} onChange={this.handleTabChange} />
          <UserCountStatistics dimension={'innerBlacklistQuantity'} />
        </div>
        {this.currentTab === 'single' && <AddBlacklistForm formData={this.params.data} ref="form" disabled={this.params.disabled} />}
        {this.currentTab === 'bulk' && (
          <ModalUpload
            ref="uploadBlackList"
            modelType={'黑名单'}
            dlinkUrl={IMPORT_BLACKLIST_TEMPLATE_URL}
            action={(file) =>
              `/rover/batch/import/blacklist/excel/parse?${qs.stringify({
                fileName: file.name,
              })}`
            }
            onSuccess={(data) => {
              this.onCancel();
              const { href } = this.$router.resolve({
                path: 'internal-blacklist/upload-confirm',
                params: {
                  type: 'blacklist/internal-blacklist',
                },
                query: {
                  batchId: data.batchId,
                },
              });
              window.location.href = href;
              this.$track(createTrackEvent(6232, '内部黑名单', '上传文件成功'));
            }}
          />
        )}
      </QModal>
    );
  },
});

export default AddBlackListModal;
