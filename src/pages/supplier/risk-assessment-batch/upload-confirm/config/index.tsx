import { cloneDeep, get, omit } from 'lodash';
import moment from 'moment';

import CompanyStatus from '@/components/global/q-company-status';
import { statusCodeMap } from '@/components/global/q-company-status/config';
import PartnerTag from '@/shared/components/partner-tag';
import { BLACKLIST_DURATION_MAP } from '@/shared/constants/black-list.constants';

const BASICCOLUMNS = [
  {
    title: '序号',
    width: 50,
    align: 'center',
    fixed: 'left',
    customRender: (item, record, index) => {
      return index + 1;
    },
  },
  {
    title: '企业名称',
    width: 380,
    fixed: 'left',
    scopedSlots: {
      customRender: 'company',
    },
  },
  {
    title: '操作',
    width: 60,
    fixed: 'right',
    scopedSlots: {
      customRender: 'action',
    },
  },
];

const batchInvestigationColumns = [
  {
    title: '登记状态',
    width: 160,
    stayOri: true,
    customRender: (item) => {
      if (item.flag) {
        return {
          children: (
            <em>
              {item.flag === 1
                ? '注册地在中国香港、中国澳门、中国台湾以及境外的企业、机关单位等暂不支持风险排查'
                : '未匹配到相关境内企业，请继续编辑'}
            </em>
          ),
          attrs: {
            colSpan: 6,
          },
        };
      }
      if (!item.status) return null;
      return <CompanyStatus status={statusCodeMap[item.statuscode]} />;
    },
  },
  {
    title: '统一社会信用代码',
    dataIndex: 'creditcode',
  },
  {
    title: '法定代表人',
    dataIndex: 'opername',
  },
  {
    title: '注册资本',
    width: 180,
    dataIndex: 'registcapi',
  },
  {
    title: '实缴资本',
    width: 180,
    dataIndex: 'reccap',
  },
  {
    title: '成立日期',
    width: 140,
    dataIndex: 'startdatecode',
    customRender: (value) => {
      return moment(value.toString()).format('YYYY-MM-DD');
    },
  },
];

const partnerColumns = [
  {
    title: '登记状态',
    width: 136,
    dataIndex: 'statuscode',
    customRender: (val) => {
      if (!statusCodeMap[val]) return null;
      return <CompanyStatus status={statusCodeMap[val]} ghost />;
    },
  },
  {
    title: '企业分组',
    stayOri: true,
    customRender: (item) => {
      if (item.flag) {
        return {
          children: (
            <em>
              {item.flag === 1
                ? '注册地在中国香港、中国澳门、中国台湾以及境外的企业、机关单位等暂不支持风险排查'
                : '未匹配到相关境内企业，请继续编辑'}
            </em>
          ),
          attrs: {
            colSpan: 4,
          },
        };
      }
      return item.parsedItem?.group || '-';
    },
  },
  {
    title: '标签',
    key: 'label',
    dataIndex: 'parsedItem',
    customRender: (data) => {
      if (data?.label?.length > 0) {
        return (
          <div class="flex flex-wrap" style="gap: 5px 0;">
            {data.label.map((name) => (
              <PartnerTag style="width: 200px;cursor: default">{name}</PartnerTag>
            ))}
          </div>
        );
      }
      return '-';
    },
  },
  {
    title: '部门',
    key: 'departmentNames',
    dataIndex: 'parsedItem',
    customRender: (data) => {
      return data?.departmentNames ? data.departmentNames.join(',') : '-';
    },
  },
  {
    title: '负责人',
    key: 'principal',
    dataIndex: 'parsedItem',
    customRender: (data) => {
      return data?.principal || '-';
    },
  },
];

const blacklistColumns = [
  {
    title: '企业分组',
    stayOri: true,
    customRender: (item) => {
      if (item.flag) {
        return {
          children: (
            <em>
              {item.flag === 1
                ? '注册地在中国香港、中国澳门、中国台湾以及境外的企业、机关单位等暂不支持风险排查'
                : '未匹配到相关境内企业，请继续编辑'}
            </em>
          ),
          attrs: {
            colSpan: 5,
          },
        };
      }
      return item.parsedItem?.group || '-';
    },
  },
  {
    title: '标签',
    key: 'label',
    dataIndex: 'parsedItem',
    customRender: (data) => {
      if (data?.label?.length > 0) {
        return (
          <div class="flex flex-wrap" style="gap: 5px 0;">
            {data.label.map((name) => (
              <PartnerTag style="width: 200px;cursor: default">{name}</PartnerTag>
            ))}
          </div>
        );
      }
      return '-';
    },
  },
  {
    title: '列入原因',
    key: 'reason',
    dataIndex: 'parsedItem',
    customRender: (data) => {
      return data?.reason || '-';
    },
  },
  {
    title: '列入时间',
    key: 'joinDate',
    dataIndex: 'parsedItem',
    customRender: (data) => {
      return data?.joinDate ? moment(data.joinDate).format('YYYY-MM-DD') : '-';
    },
  },
  {
    title: '有效期',
    key: 'duration',
    dataIndex: 'parsedItem',
    customRender: (data) => {
      return BLACKLIST_DURATION_MAP[data?.duration] || '-';
    },
  },
  {
    title: '截止日期',
    key: 'expiredDate',
    dataIndex: 'parsedItem',
    customRender: (data) => {
      return data?.expiredDate ? moment(data.expiredDate).format('YYYY-MM-DD') : '-';
    },
  },
  {
    title: '来源部门',
    key: 'departmentNames',
    dataIndex: 'parsedItem',
    customRender: (data) => {
      return data?.departmentNames ? data.departmentNames.join(',') : '-';
    },
  },
];

const riskMonitorColumns = [
  {
    title: '登记状态',
    width: 160,
    stayOri: true,
    customRender: (item) => {
      if (item.flag) {
        return {
          children: (
            <em>
              {item.flag === 1
                ? '注册地在中国香港、中国澳门、中国台湾以及境外的企业、机关单位等暂不支持风险排查'
                : '未匹配到相关境内企业，请继续编辑'}
            </em>
          ),
          attrs: {
            colSpan: 2,
          },
        };
      }
      if (!item.status) return null;
      return <CompanyStatus status={statusCodeMap[item.statuscode]} />;
    },
  },
  {
    title: '企业分组',
    dataIndex: 'parsedItem.group',
  },
];
const generateColumns = (coloums) => {
  const extra = coloums.map((coloum) => {
    if (coloum.stayOri) {
      return coloum;
    }
    const { dataIndex, customRender: customRenderFn } = coloum;
    return {
      ...omit(coloum, ['dataIndex', 'customRender']),
      customRender: (item) => {
        if (item.flag) {
          return {
            attrs: {
              colSpan: 0,
            },
          };
        }
        if (customRenderFn) {
          return customRenderFn(item[dataIndex]);
        }
        return get(item, dataIndex, '-');
      },
    };
  });
  const basicColoums = cloneDeep(BASICCOLUMNS);
  basicColoums.splice(-1, 0, ...extra);
  if (basicColoums.length <= 5) {
    basicColoums[1].width = 500;
  }
  return basicColoums;
};

export const RouterConfigMap = {
  'batch-investigation': {
    backName: 'batch-investigation',
    breadLabel: '批量排查',
    columns: generateColumns(batchInvestigationColumns),
    confirmLabel: '开始排查',
  },
  'potential-investigation/batch': {
    backName: 'potential-batch',
    breadLabel: '批量排查',
    columns: generateColumns(batchInvestigationColumns),
    confirmLabel: '开始排查',
    queryUrl: '/batch/import/potential/excel/item/search',
    editUrl: '/batch/import/potential/excel/item',
    deleteUrl: '/batch/import/potential/excel/item',
    saveUrl: '/batch/import/potential/excel/execute',
  },
  'third-party/partners': {
    backPath: '/supplier/third-party/partners',
    backName: 'supplier-partners',
    breadLabel: '批量导入',
    columns: generateColumns(partnerColumns),
    confirmLabel: '添加至第三方',
    warningText: '若上传企业已存在于第三方列表时，是否更新该企业信息',
    queryUrl: '/batch/import/customer/excel/item/search',
    editUrl: '/batch/import/customer/excel/item',
    deleteUrl: '/batch/import/customer/excel/item',
    saveUrl: '/batch/import/customer/excel/execute',
    canChangeMode: true,
  },
  'third-party-partners': {
    backPath: '/supplier/third-party-partners',
    backName: 'supplier-partners',
    breadLabel: '批量导入',
    columns: generateColumns(partnerColumns),
    confirmLabel: '添加至相对方列表',
    warningText: '若上传企业已存在于相对方列表时，是否更新该企业信息',
    queryUrl: '/batch/import/customer/excel/item/search',
    editUrl: '/batch/import/customer/excel/item',
    deleteUrl: '/batch/import/customer/excel/item',
    saveUrl: '/batch/import/customer/excel/execute',
    canChangeMode: true,
  },
  'blacklist/internal-blacklist': {
    backName: 'internal-blacklist',
    breadLabel: '批量导入',
    columns: generateColumns(blacklistColumns),
    confirmLabel: '添加至黑名单',
    warningText: '若上传企业已存在于内部黑名单列表时，是否更新该企业信息',
    queryUrl: '/batch/import/blacklist/excel/item/search',
    editUrl: '/batch/import/blacklist/excel/item',
    deleteUrl: '/batch/import/blacklist/excel/item',
    saveUrl: '/batch/import/blacklist/excel/execute',
    canChangeMode: true,
  },
  'risk-monitor': {
    backName: 'risk-monitor',
    breadLabel: '批量导入',
    columns: generateColumns(riskMonitorColumns),
    confirmLabel: '添加至监控列表',
    warningText: '若上传企业已存在于合作监控列表时，是否更新该企业信息',
    queryUrl: '/batch/import/monitor/excel/item/search',
    editUrl: '/batch/import/monitor/excel/item',
    deleteUrl: '/batch/import/monitor/excel/item',
    saveUrl: '/batch/import/monitor/excel/execute',
    canChangeMode: true,
  },
};

export const getRouterConfig = (type) => {
  const config = RouterConfigMap[type];
  if (config) {
    return config;
  }
  return RouterConfigMap['third-party/partners'];
};
