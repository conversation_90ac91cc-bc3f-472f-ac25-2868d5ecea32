import { computed, defineComponent, onMounted, reactive, ref, unref } from 'vue';
import { useRoute, useRouter } from 'vue-router/composables';
import { Breadcrumb, Button, Radio, Icon, message } from 'ant-design-vue';

import { isLatestPage, isLatestRow } from '@/utils/pagination';
import { batchImport } from '@/shared/services';
import { useSearch } from '@/shared/composables/use-search';
import HeroicLayout from '@/shared/layouts/heroic';
import QCard from '@/components/global/q-card';
import { createTrackEvent, useTrack } from '@/config/tracking-events';

import BatchConfirmResult from './widgets/bacth-confirm-result';
import styles from './upload-confirm.module.less';
import { getRouterConfig } from './config/index';

const BachUploadConfirm = defineComponent({
  name: 'BachUploadConfirm',
  setup() {
    const track = useTrack();
    const init = ref(false);
    const route = useRoute();
    const router = useRouter();
    const batchId = route.query.batchId;
    const selectedIds = ref([]);
    const pagination = reactive({
      pageSize: 10,
      current: 1,
      total: 0,
    });
    const statistic = reactive({
      duplicatedCount: 0,
      errorCount: 0,
      successCount: 0,
    });

    const btnLoading = ref(false);
    const pageConfig = ref(getRouterConfig(route.params.type));
    const isUpdateMode = ref(true);

    const dataSource = ref([]);

    const getParams = () => {
      return {
        batchId: +batchId,
        pageSize: pagination.pageSize,
        pageIndex: pagination.current,
      };
    };

    const searchRequest = async () => {
      if (!batchId) {
        message.error('请先上传企业');
        return;
      }
      const res = await batchImport.getUploadData(getParams(), pageConfig.value.queryUrl);
      dataSource.value = res.data || [];
      pagination.current = res.pageIndex || 0;
      pagination.total = res.total || 0;
      pagination.pageSize = res.pageSize || 0;
      Object.assign(statistic, res.statistic);
    };
    const { search, isLoading } = useSearch(searchRequest);
    // 是否需要返回第一页
    const isNeedGoFirst = computed(() => {
      if (isLatestRow(pagination)) return true;
      return isLatestPage(pagination) && unref(dataSource)?.length > 0 && unref(dataSource).length === unref(selectedIds).length;
    });

    const pageChange = (current, pageSize) => {
      pagination.current = current;
      pagination.pageSize = pageSize;
      selectedIds.value = [];
      search();
    };

    const updateData = (data) => {
      batchImport.updateCompany(data, pageConfig.value.editUrl).then(() => {
        message.success('修改成功');
        search();
      });
    };

    const hideBtn = computed(() => {
      return unref(dataSource).length === 0 || unref(dataSource).some((item: any) => item.flag);
    });

    const handleDelete = async (flag = false) => {
      const param = flag
        ? {
            flag,
            batchId,
          }
        : {
            ids: unref(selectedIds),
            batchId,
          };
      await batchImport.deleteCompany(param, pageConfig.value.deleteUrl);
      message.success('移除成功');
      // 只剩最后一条时，返回到第一页
      if (isNeedGoFirst.value) {
        pagination.current = 1;
      }
      selectedIds.value = [];
      search();
    };

    // 少于5家企业，直接进入风险排查
    const skipBatchInvestigation = () => {
      const ids: any = [];
      const names: any = [];
      dataSource.value.forEach((item: any) => {
        ids.push(item.companyId);
        names.push(item.name);
      });
      router.push({
        name: 'supplier-investigate-detail',
        params: {
          type: 'batch-investigation',
          id: (dataSource.value[0] as any).companyId,
        },
        query: {
          ids: ids.join(','),
          names: names.join(','),
          from: 'assessment',
        },
      });
    };

    // 批量风险排查、第三方列表、内部黑名单、监控列表
    const executeBatch = () => {
      btnLoading.value = true;
      batchImport
        .excuteDiligence({ isUpdate: unref(isUpdateMode), batchId }, pageConfig.value.saveUrl)
        .then(() => {
          message.success('批量任务已执行！请稍后在批量排查任务中查看排查结果');
          const routeConfig = pageConfig.value.backPath ? { path: pageConfig.value.backPath } : { name: pageConfig.value.backName };
          router.push(routeConfig);
        })
        .finally(() => {
          btnLoading.value = false;
        });
    };

    const execute = () => {
      track(createTrackEvent(6959, '企业核实', pageConfig.value.confirmLabel));
      const type = route.params.type;
      if (type === 'batch-investigation' && pagination.total <= 5) {
        skipBatchInvestigation();
        return;
      }
      if (unref(btnLoading)) {
        return;
      }
      executeBatch();
    };

    onMounted(async () => {
      if (!batchId) {
        message.error('请先上传企业');
      } else {
        await search();
      }
      init.value = false;
    });
    return {
      init,
      route,
      batchId,
      dataSource,
      isLoading,
      pagination,
      selectedIds,
      hideBtn,
      statistic,
      updateData,
      search,
      handleDelete,
      pageChange,
      execute,
      pageConfig,
      btnLoading,
      isUpdateMode,
    };
  },
  render() {
    const { dataSource, isLoading, selectedIds, pageConfig } = this;
    const routeList = [{ id: 'batch-investigation', route: `/supplier/${this.$route.params.type}`, name: pageConfig.breadLabel }];
    const pagination = {
      ...this.pagination,
      onChange: this.pageChange,
      onShowSizeChange: this.pageChange,
    } as any;
    return (
      <HeroicLayout loading={this.init}>
        <div class="flex justify-between breadcrumb items-center">
          <Breadcrumb>
            {routeList.map((v: any, index: number) => {
              return (
                <Breadcrumb.Item key={v.id}>
                  <a
                    class={{ firstItem: index === 0 }}
                    onClick={() => {
                      this.$track(createTrackEvent(6959, '企业核实', '返回上一页'));
                      // 从搜索结果列表进入时，直接返回上一级
                      if (v.id === 'investigateList' && this.$route.query.query === 'list') {
                        this.$router.back();
                        return;
                      }
                      if (v.route === 'back' || v.route?.path === 'back') {
                        this.$router.back();
                      } else {
                        this.$router.push(v.route);
                      }
                    }}
                  >
                    {index === 0 ? <q-icon type="icon-mianbaoxiefanhui"></q-icon> : null} {v.name}
                  </a>
                </Breadcrumb.Item>
              );
            })}
            <Breadcrumb.Item>企业核实</Breadcrumb.Item>
          </Breadcrumb>
        </div>
        <QCard bodyStyle={{ padding: '15px' }} rootStyle={{ paddingTop: '40px' }}>
          <div slot="title">
            <div class="flex">
              <div class="flex">
                当前匹配成功
                <Icon v-show={this.isLoading} type="sync" spin />
                <em v-show={!this.isLoading}>{this.statistic.successCount}</em>
                家企业，
              </div>
              <div class="flex">
                匹配失败
                <Icon v-show={this.isLoading} type="sync" spin />
                <em v-show={!this.isLoading}>{this.statistic.errorCount}</em>
                条信息，
              </div>
              <div class="flex">
                导入重复企业
                <Icon v-show={this.isLoading} type="sync" spin />
                <em v-show={!this.isLoading}>{this.statistic.duplicatedCount}</em>条
              </div>
            </div>
          </div>
          <div class={styles.btnGroup} slot="extra">
            <Button
              disabled={this.statistic.errorCount === 0}
              onClick={() => {
                this.handleDelete(true);
                this.$track(createTrackEvent(6959, '企业核实', '过滤匹配失败名称'));
              }}
            >
              移除匹配失败名称
            </Button>
            <Button
              onClick={() => {
                if (this.selectedIds.length === 0) {
                  message.warning('请先勾选企业');
                  return;
                }
                this.handleDelete();
                this.$track(createTrackEvent(6959, '企业核实', '移除列表'));
              }}
            >
              移除列表
            </Button>
          </div>
          <BatchConfirmResult
            selectedIds={selectedIds}
            dataSource={dataSource}
            columns={pageConfig.columns}
            pagination={pagination}
            loading={isLoading}
            onSelect={(values) => {
              this.selectedIds = values.map((item) => {
                return item.id;
              });
            }}
            onChange={(data) => {
              this.updateData(data);
            }}
            onUpdate={() => {
              this.search();
            }}
            // onChange={this.updateSortInfo}
            onDelete={(ids) => {
              this.selectedIds = ids;
              this.handleDelete();
              this.$track(createTrackEvent(6959, '企业核实', '移除'));
            }}
          ></BatchConfirmResult>

          <div class={styles.fixbtn}>
            {pageConfig.canChangeMode && (
              <div class="flex items-center">
                <span class={styles.tipsText}>{pageConfig.warningText}：</span>
                <Radio.Group v-model={this.isUpdateMode}>
                  <Radio value={true}>更新</Radio>
                  <Radio value={false}>不更新</Radio>
                </Radio.Group>
              </div>
            )}
            <Button type="primary" onClick={this.execute} disabled={this.hideBtn} loading={this.btnLoading}>
              {pageConfig.confirmLabel}
            </Button>
          </div>
        </QCard>
      </HeroicLayout>
    );
  },
});

export default BachUploadConfirm;
