.fixbtn {
  position: absolute;
  width: calc(100% - 30px);
  bottom: 0;
  text-align: right;
  padding: 15px 0;
  border-top: 1px solid #eee;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: flex-end;

  :global {
    .ant-btn-primary[disabled] {
      color: #fff;
      background-color: #b8dcfa;
      border-color: transparent;
    }
  }
}

.btn-group {
  display: flex;
  gap: 10px;
}

.originalname {
  white-space: wrap;
  text-overflow: ellipsis;
  overflow: hidden;
  color: #999;
}

.empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  > * {
    margin-bottom: 30px;
  }

  .single-add {
    margin-right: 12px;
  }
}

.tableData{
  em{
    color:  rgb(240, 64, 64);
  }

  :global{
    td{
      height: 51px;
    }
  }
}