import { defineComponent, PropType, unref } from 'vue';

import { uesInvestStore } from '@/hooks/use-invest-store';
import { createTrackEvent } from '@/config/tracking-events';
import { RISK_LEVEL_CODE_MAP } from '@/shared/constants/risk-level-code-map.constant';

import styles from './risk-filter.module.less';
import RiskWarning from '../risk-warning';
import EmptyResult from '../risk-content/common/empty-result';
import { useStore } from '@/store';

const iconMap = {
  0: 'icon-tongguo',
  1: 'icon-jinshenheshi',
  2: 'icon-butongguo',
};

const RiskFilter = defineComponent({
  name: 'RiskFilter',
  props: {
    dimensions: {
      type: Array as PropType<any>,
      default: () => [],
    },
  },
  setup() {
    const { activeTabKey: selectIndex, activeTab: selectValue, updateActiveTab } = uesInvestStore();
    const store = useStore();

    const updateSelect = (key) => {
      updateActiveTab(key);
    };

    return {
      selectIndex,
      selectValue,
      updateSelect,
    };
  },
  render() {
    const { selectIndex } = this;
    const { tabs, riskInfo } = uesInvestStore();
    const levelGroup = unref(riskInfo).details?.levelGroup || [];

    const getRiskData = (level) => {
      const list = levelGroup[level];
      if (selectIndex === 'all') {
        return list;
      }
      return list.filter((v) => v.groupKey === selectIndex);
    };
    const selectedGroupNodes = [2, 1, 0].map((v) => {
      const risk = getRiskData(v) || [];
      return <RiskWarning key={v} type={RISK_LEVEL_CODE_MAP[v]} v-show={risk.length} dataList={risk} />;
    });

    return (
      <div class={styles.container}>
        <div class={styles.itemList}>
          {unref(tabs).map((v: any) => {
            return (
              <div
                class={{ [styles.item]: true, [styles.active]: selectIndex === v.groupKey }}
                onClick={() => {
                  this.updateSelect(v.groupKey);
                  this.$track(createTrackEvent(6206, '准入排查详情页', v.name));
                }}
              >
                <q-icon v-show={iconMap[v.level]} class={[styles[`icon${v.level}`], styles.icon]} type={iconMap[v.level]} />
                {v.name}
              </div>
            );
          })}
        </div>
        <div class={styles.riskContent}>
          {/* 显示当前选中风险维度 */}
          <EmptyResult value={this.selectValue}>
            {/* 如果不为空,显示当前选中的风险 */}
            {/* 如果关联设置中指标类型为关键项，无风险项也展示出来 */}
            {this.selectValue?.totalHits || this.selectValue?.scoreDetails?.length ? selectedGroupNodes : null}
          </EmptyResult>
        </div>
      </div>
    );
  },
});

export default RiskFilter;
