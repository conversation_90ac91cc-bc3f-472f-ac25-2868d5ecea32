import { computed, defineComponent, inject, PropType } from 'vue';
import { useRoute } from 'vue-router/composables';

import { uesInvestStore } from '@/hooks/use-invest-store';
import { useUserStore } from '@/shared/composables/use-user-store';
import { RiskLevel, RISK_LEVEL_CODE_MAP } from '@/shared/constants/risk-level-code-map.constant';
import { createTrackEvent } from '@/config/tracking-events';
import RiskDimensionHeading from '@/shared/components/risk-dimension-heading';

import DynamicCollapseBlock from '../dynamic-collapse-block';
import DynamicCollapseMultipleBlock from '../dynamic-collapse-multiple-block';
import styles from './risk-group.module.less';
import RiskLevelHeading from '../../common/risk-level-heading';

const SOURCE_LIST = ['record', 'statistics', 'assessment-batch', 'risk-trends'];

const RiskGroup = defineComponent({
  name: 'RiskGroup',
  props: {
    timeStamp: {
      type: [String, Number],
      default: '',
    },
    // 展示的数据
    diligenceData: {
      type: Array as PropType<any[]>,
      default: () => [],
    },
  },
  setup() {
    const snapshotId = inject('snapshotId');
    const { expandKeys, companyInfo, updateExpandKeys } = uesInvestStore();
    const { isZeiss } = useUserStore();

    const route = useRoute();
    const from = computed(() => (SOURCE_LIST.includes(route.query?.from as string) ? 'record' : ''));

    return {
      snapshotId,
      expandKeys,
      isZeiss,
      companyInfo,
      from,
      updateExpandKeys,
    };
  },
  render() {
    const detailRenderer = ({
      detail,
      meta,
      level,
      hasTag,
      hasArrow,
    }: {
      detail: Record<string, any>;
      meta: Record<string, any>;
      level: RiskLevel;
      hasTag: boolean;
      hasArrow: boolean;
    }) => {
      return (
        <div class={styles.collapseWrapper}>
          <DynamicCollapseBlock
            id={meta.key}
            disabled={detail.totalHits === 0}
            meta={{ ...meta, from: this.from, tkey: `${this.timeStamp}_${meta.key}` }}
            defaultCollapse={!this.expandKeys.includes(meta.key) || detail.totalHits === 0}
            onToggle={(isCollapse) => {
              let type = '展开';
              let updateKeys = [...this.expandKeys, meta.key];
              if (isCollapse) {
                type = '折叠';
                updateKeys = this.expandKeys.filter((v) => v !== meta.key);
              }
              this.updateExpandKeys(updateKeys);
              this.$track(createTrackEvent(6204, '准入排查详情页', type));
            }}
            arrow={hasArrow}
            totalHits={detail.totalHits}
            description={detail.description}
            scopedSlots={{
              header: () => {
                return (
                  <div class={styles.collapseHeader}>
                    <RiskDimensionHeading level={level} showTag={hasTag} content={detail.description} />
                  </div>
                );
              },
              content: (node) => {
                return <div class={styles.collapseBody}>{node}</div>;
              },
            }}
          />
        </div>
      );
    };

    const dimensionRenderer = (dm, level) => {
      if (Array.isArray(dm.subDimension) && dm.subDimension.length > 0) {
        return (
          <div class={styles.collapseWrapper}>
            <DynamicCollapseMultipleBlock
              scopedSlots={{
                header: () => {
                  return (
                    <div class={styles.collapseHeader}>
                      <RiskDimensionHeading level={dm.level} content={dm.description} />
                    </div>
                  );
                },
              }}
            >
              <div class={styles.collapseBody} style={{ paddingBottom: 0 }}>
                {dm.subDimension.map((subDm) => {
                  return (
                    <div key={subDm.key} class={styles.body}>
                      {/* 特殊嵌套维度样式问题 */}
                      {detailRenderer({
                        detail: subDm,
                        meta: {
                          source: dm.source,
                          key: dm.key,
                          keyNo: this.companyInfo.KeyNo,
                          companyName: this.companyInfo.Name,
                          subDimensionKey: subDm.key,
                          proDetailId: subDm.proDetailId,
                          snapshotId: this.snapshotId,
                          // pageIndex: 1,
                          // pageSize: 5,
                        },
                        level,
                        hasTag: false,
                        hasArrow: false,
                      })}
                    </div>
                  );
                })}
              </div>
            </DynamicCollapseMultipleBlock>
          </div>
        );
      }

      return detailRenderer({
        detail: dm,
        meta: {
          source: dm.source,
          key: dm.key,
          keyNo: this.companyInfo.KeyNo,
          companyName: this.companyInfo.Name,
          proDetailId: dm.proDetailId,
          snapshotId: this.snapshotId,
          // pageIndex: 1,
          // pageSize: 5,
        },
        level,
        hasTag: true,
        hasArrow: true,
      });
    };

    return (
      <div class={styles.container}>
        {this.diligenceData
          .filter(({ dataSource }) => dataSource.length) // dataSource 为空时跳过
          .map(({ level, dataSource }) => {
            // 非蔡司不计算命中项总数
            const totalHits = this.isZeiss
              ? dataSource.reduce((acc: number, cur: Record<string, any>) => acc + cur.scoreDetails.length, 0)
              : 0;

            return (
              <div class={[styles.group, styles[RISK_LEVEL_CODE_MAP[level]]]} key={`${dataSource[0]?.groupKey}${level}`}>
                {/* 非蔡司不展示数据统计 */}
                {this.isZeiss ? <RiskLevelHeading level={level}>({totalHits}项)</RiskLevelHeading> : null}

                {Array.isArray(dataSource)
                  ? dataSource.map((groupItem) => {
                      return (
                        <div key={groupItem.groupKey} class={styles.block}>
                          {/* 隐藏顶级层次 */}
                          {this.isZeiss ? null : (
                            <div class={styles.title}>
                              <span>{groupItem.name}</span>
                              <em>{groupItem.totalHits}</em>
                            </div>
                          )}
                          {/* <pre>{JSON.stringify(groupItem.scoreDetails)}</pre> */}
                          {Array.isArray(groupItem.scoreDetails)
                            ? groupItem.scoreDetails.map((detail, index) => {
                                const isLastLayer = !detail.subDimension; // scoreDetails 为最终维度
                                if (isLastLayer) {
                                  return (
                                    <div key={`${detail.key}_${index}`} class={styles.dimension}>
                                      <div class={styles.body}>
                                        {detailRenderer({
                                          detail,
                                          meta: {
                                            source: detail.source,
                                            key: detail.key,
                                            keyNo: this.companyInfo.KeyNo,
                                            companyName: this.companyInfo.Name,
                                            proDetailId: detail.proDetailId,
                                            snapshotId: this.snapshotId,
                                            // pageIndex: 1,
                                            // pageSize: 5,
                                          },
                                          level,
                                          hasTag: true,
                                          hasArrow: true,
                                        })}
                                      </div>
                                    </div>
                                  );
                                }
                                return (
                                  <div key={`${detail.key}_${index}`} class={styles.dimension}>
                                    <div class={styles.header} data-dimension-key={detail.key}>
                                      <span>{detail.name}</span>
                                      <em v-show={detail.totalHits > 0}>{detail.totalHits}</em>
                                    </div>
                                    <div class={styles.body}>
                                      {/* 二级维度展开 */}
                                      {Array.isArray(detail.subDimension)
                                        ? detail.subDimension.map((subDm) => {
                                            return <div key={subDm.key}>{dimensionRenderer(subDm, level)}</div>;
                                          })
                                        : null}
                                    </div>
                                  </div>
                                );
                              })
                            : null}
                        </div>
                      );
                    })
                  : null}
              </div>
            );
          })}
      </div>
    );
  },
});

export default RiskGroup;
