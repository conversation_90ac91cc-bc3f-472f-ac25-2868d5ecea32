import { computed, defineComponent, PropType, ref } from 'vue';

import { uesInvestStore } from '@/hooks/use-invest-store';
import { useI18n } from '@/shared/composables/use-i18n';
import QCard from '@/components/global/q-card';
import FullWatermark from '@/components/full-watermark';
import { RiskLevel } from '@/shared/constants/risk-level-code-map.constant';
import CollapseBlock from '@/components/collapse-block';

import BasicInfo from '../../basic-info/zeiss';
import RiskGroup from './risk-group';
import EmptyResult from '../common/empty-result';
import RiskQualification, { type CertificationObj } from '../../risk-qualification';
import AIReportGuide from '../../ai-report-guide';

const RISK_LEVELS = [RiskLevel.High, RiskLevel.Middle, RiskLevel.Low];

const RiskContentZeiss = defineComponent({
  name: 'RiskContentZeiss',
  props: {
    loading: {
      type: Boolean,
      default: true,
    },
    dimensions: {
      type: Array as PropType<Record<string, any>>,
      default: () => [],
    },
    company: {
      type: Object,
      required: true,
    },
    riskLevel: {
      type: Number,
      required: true,
    },
    tabs: {
      type: Array as PropType<Record<string, any>>,
      default: () => [],
    },
    qualifications: {
      type: Object as PropType<CertificationObj>,
      default: () => ({}),
    },
    credits: {
      type: Object as PropType<Record<string, any>>,
      default: () => ({}),
    },
    /**
     * 面包屑的高度
     */
    breadcrumbHeight: {
      type: Number,
      default: 0,
    },
    partnerTags: {
      type: Array as PropType<any[]>,
      default: () => [],
    },
  },
  setup() {
    const { expandKeys, riskInfo, companyInfo, updateExpandKeys } = uesInvestStore();
    const timeStamp = ref(new Date().getTime());
    // Watermark size
    const watermarkHeight = computed(() => window.innerHeight - 102);

    return {
      timeStamp,
      expandKeys,
      riskInfo,
      companyInfo,
      watermarkHeight,
      updateExpandKeys,
    };
  },
  render() {
    const { tc } = useI18n();
    if (this.loading) {
      return <div style="height: 0" />;
    }
    return (
      <FullWatermark
        visible={!!this.riskInfo.snapshotId}
        height={this.watermarkHeight}
        offset={{
          x: 190,
          y: 102,
        }}
        fillOffset={{
          x: 300,
          y: 300,
        }}
      >
        <QCard
          rootStyle={{
            borderRadius: this.tabs.length > 1 ? '0 0 4px 4px' : '4px',
          }}
          bodyStyle={{
            padding: '10px 15px 15px 15px',
          }}
        >
          <BasicInfo company={this.companyInfo} tags={this.partnerTags} riskLevel={this.riskLevel} dimensions={this.dimensions}>
            <template slot="extra">{this.$slots?.extra}</template>;
          </BasicInfo>
        </QCard>

        <RiskQualification qualifications={this.qualifications} credits={this.credits} />

        {/* AI解读分析模块 */}
        {this.riskInfo.id ? <AIReportGuide diligenceId={this.riskInfo.id} /> : null}

        {this.riskInfo.details?.dimensionScoreDetails?.map((dm) => {
          const total = dm.totalHits ? `<em>${dm.totalHits}</em>` : '';
          const title = `<span>${tc(dm.groupKey)}</span>`;
          const groupByLevel = (level: RiskLevel) =>
            this.riskInfo.details?.levelGroup?.[level].filter((item) => item.groupKey === dm.groupKey);

          const levelGroups = RISK_LEVELS.map((level) => {
            return {
              level,
              dataSource: groupByLevel(level),
              expandKeys: this.expandKeys,
            };
          });
          return (
            <CollapseBlock
              title={[title, total].join(' ')}
              key={dm.groupKey}
              defaultCollapse={!this.expandKeys.includes(dm.groupKey)}
              onToggle={(isCollapse) => {
                this.updateExpandKeys(isCollapse ? this.expandKeys.filter((v) => v !== dm.groupKey) : [...this.expandKeys, dm.groupKey]);
              }}
            >
              {dm.totalHits > 0 ? (
                <RiskGroup timeStamp={this.timeStamp} diligenceData={levelGroups} />
              ) : (
                <EmptyResult value={dm}>
                  {dm.scoreDetails.length > 0 ? <RiskGroup timeStamp={this.timeStamp} diligenceData={levelGroups} /> : null}
                </EmptyResult>
              )}
            </CollapseBlock>
          );
        })}
      </FullWatermark>
    );
  },
});

export default RiskContentZeiss;
