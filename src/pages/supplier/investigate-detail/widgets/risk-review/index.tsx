import { defineComponent, nextTick, onMounted, unref } from 'vue';

import { uesInvestStore } from '@/hooks/use-invest-store';
import DiligenceWarningBlockV2 from '@/components/diligence-waring-block-v2';
import { createTrackEvent } from '@/config/tracking-events';

const RiskReview = defineComponent({
  name: 'RiskReview',
  props: {
    /**
     * 距离页面顶部的距离
     */
    scrollOffsetTop: {
      type: Number,
      default: 0,
    },
  },
  setup(props) {
    const { updateExpandKeys } = uesInvestStore();
    const scrollToView = async (id = window.location?.hash?.replace('#', ''), event?: Event) => {
      await nextTick();
      if (!id) {
        return;
      }
      // 展开对应的风险维度模块
      updateExpandKeys([id]);
      const element = document.getElementById(id);
      if (!element) {
        return;
      }

      // 取消默认行为（锚点）
      event?.stopPropagation?.();
      event?.preventDefault?.();

      // 滚动到对应的风险维度模块
      element.scrollIntoView({ block: 'start' });

      // 回弹到正确位置（目前由于 滚动区域 和顶部导航的问题，需要额外的偏移量才能完整显示）
      if (props.scrollOffsetTop !== 0) {
        // const offsetTop = -50; // 向上偏移量
        const offsetTop = props.scrollOffsetTop * -1; // 向上偏移量
        const scrollWrapper = document.querySelector('.workbench-layout-main') || window;
        scrollWrapper.scrollBy(0, offsetTop);
      }
    };

    onMounted(() => {
      scrollToView();
    });

    return {
      scrollToView,
    };
  },
  render() {
    const { riskLevel, riskInfo, activeTabKey } = uesInvestStore();
    return (
      <DiligenceWarningBlockV2
        riskLevel={unref(riskLevel)}
        riskInfo={unref(riskInfo)}
        activeTabKey={unref(activeTabKey)}
        scrollToView={(id, item, event) => {
          this.$track(createTrackEvent(6985, '准入排查详情页', item.name, '排查结果'));
          activeTabKey.value = 'all';
          this.scrollToView(id, event);
        }}
      />
    );
  },
});

export default RiskReview;
