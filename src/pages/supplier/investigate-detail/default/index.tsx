import _, { omit } from 'lodash';
import { Breadcrumb, message, Tooltip } from 'ant-design-vue';
import { computed, defineComponent, onBeforeUnmount, provide, ref, shallowReactive, unref, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router/composables';
import { Route } from 'vue-router';
import { useToggle } from '@vueuse/core';
import moment from 'moment';
import axios from 'axios';

import { useStore } from '@/store';
import { diligence as diligenceService } from '@/shared/services';
import HeroicLayout from '@/shared/layouts/heroic';
import CompanySearch from '@/shared/components/company-search';
import RiskAction from '@/shared/components/risk-action';
import { useAbility } from '@/libs/plugins/user-ability';
import Empty from '@/shared/components/empty';
import { ERROR_MESSAGES } from '@/config/message.config';
import { useI18n } from '@/shared/composables/use-i18n';
import { createTrackEvent, useTrack } from '@/config/tracking-events';
import { useWebTitle } from '@/shared/composables/use-web-title';
import { GenerateReportStatus, useGenerateReportFile } from '@/shared/composables/use-generate-report-file';
import GenerateReportAction from '@/components/generate-report-action';
import { uesInvestStore } from '@/hooks/use-invest-store';
import { BatchBusinessTypeEnum } from '@/shared/constants/batch-business-type.constant';

import RiskTab from '../widgets/risk-tabs';
import RiskContentDefault from '../widgets/risk-content/default';
import RiskContentZeiss from '../widgets/risk-content/zeiss';
import styles from './investigate-detail.page.module.less';
import { openAuditFollowDrawer } from '../widgets/audit-followup';
import RiskWatch from '../widgets/risk-watch';
import { openSettingDrawer } from '../widgets/risk-content/default/setting-drawer';

function getBodyStyle({ paddingTop }) {
  const defaultStyle = {
    background: 'none',
    display: 'flex',
    flexDirection: 'column',
    paddingTop,
  };
  return defaultStyle;
}

const InvestigateDetailPage = defineComponent({
  name: 'InvestigateDetailPage',
  provide() {
    return {
      getCompanyDetail: () => this.company,
    };
  },
  setup() {
    const ability = useAbility();
    const track = useTrack();
    const { setWebTitle } = useWebTitle();

    const loading = ref(false);
    const router = useRouter();
    const route = useRoute();
    const store = useStore();

    const {
      riskInfo,
      companyInfo: company,
      riskLevel,
      snapshotId,
      dimensionDetails,
      qualifications,
      credits,
      updateActiveTab,
      updateExpandKeys,
      updateBasicData,
    } = uesInvestStore();

    const snapshotDate = computed(() =>
      unref(riskInfo)?.createDate ? moment(unref(riskInfo)?.createDate).format('YYYY-MM-DD HH:mm:ss') : ''
    );
    // 判断组织

    const isZeissOrg = computed(() => store.getters['user/isZeissOrg']);

    // 最新的diligenceId
    const cacheDiligenceId = ref(unref(riskInfo)?.id || route?.query?.diligenceId);
    // 错误处理
    const errorOutOfLimitation = shallowReactive<{ error: boolean; message: string | undefined }>({
      error: false,
      message: undefined,
    });

    /**
     * 获取风险排查详情
     * @param keyNo company keyNo
     * @param needRefreshSnapshot 是否刷新快照：刷新快照时不传 diligenceId 和 snapshotId
     * @param isDynamicDetails 是否是排查详情
     */
    const fetchData = async (keyNo: string, needRefreshSnapshot = false, isDynamicDetails = false) => {
      try {
        loading.value = true;
        errorOutOfLimitation.error = false;
        errorOutOfLimitation.message = undefined;
        updateExpandKeys([]);
        // 请求不同公司时，清除缓存数据
        if (unref(company)?.KeyNo !== keyNo) {
          updateBasicData();
          cacheDiligenceId.value = undefined;
        }
        await store.dispatch('company/getCompanyInfo', { keyNo });
        setWebTitle(company.value?.Name);
        updateActiveTab('all');
        if (!company.value?.Name) return;
        await store.dispatch('company/getDiligence', {
          keyNo,
          diligenceId: needRefreshSnapshot ? undefined : unref(cacheDiligenceId),
          isDynamicDetails,
          // 刷新快照时需要传 `orgSettingsId`
          ambiguousSettingId: needRefreshSnapshot ? riskInfo.value.orgSettingsId : undefined,
        });

        cacheDiligenceId.value = unref(riskInfo).id;
        // 仅在重新生成快照时更新 URL
        if (needRefreshSnapshot || unref(riskInfo).notMatch) {
          await router
            .replace({
              query: {
                ...router.currentRoute.query, // 获取实时 query
                diligenceId: unref(riskInfo).id,
              },
            })
            .catch(_.noop);
        }

        // 获取资质信息
        await store.dispatch('company/getQualificationData', keyNo);
      } catch (error) {
        if (axios.isAxiosError(error)) {
          if (error.response?.data.code === 400203 && error.response?.data.statusCode === 403) {
            errorOutOfLimitation.error = true;
            errorOutOfLimitation.message = ERROR_MESSAGES.OUT_OF_DILIGENCE_LIMITATION;
          }
        } else {
          console.error(error);
        }
      } finally {
        loading.value = false;
      }
    };

    onBeforeUnmount(() => {
      updateBasicData();
      cacheDiligenceId.value = '';
    });

    const [genReportState, genReportPolling, resetGenReportState] = useGenerateReportFile(BatchBusinessTypeEnum.Diligence_Report_Export);
    const handleGenerateReport = async () => {
      track(createTrackEvent(6204, '准入排查详情页', '生成报告'));
      if (!(await ability.check('stock', ['DiligenceReportQuantity']))) {
        return;
      }
      if (!riskInfo.value?.id) {
        message.warning('数据异常，请刷新重试');
        return;
      }
      try {
        genReportState.status = GenerateReportStatus.Pending; // 提前将状态设置为 `进行中`
        const { batchId } = await diligenceService.pollingGenerateReport(riskInfo.value?.id);
        message.success('您的报告正在生成中，成功后我们将第一时间提醒您');
        genReportPolling(batchId);
      } catch (error: any) {
        genReportState.status = GenerateReportStatus.Idle;
        const errorMessage = error?.response?.data?.error ?? '报告生成失败，请稍后再试';
        message.error(errorMessage);
      }
    };

    // 生成快照
    const [refreshingSnapshot, setRefreshingSnapshot] = useToggle(false);
    const refreshSnapshot = async () => {
      track(createTrackEvent(6204, '准入排查详情页', '重新排查'));
      const hasAbility = await ability.check('stock', ['DiligenceHistoryQuantity', 'DiligenceDailyQuantity']);
      if (refreshingSnapshot.value || !hasAbility) {
        return;
      }
      setRefreshingSnapshot(true);
      const params = {
        companyId: company.value.KeyNo,
        companyName: company.value.Name,
      };
      try {
        // 刷新快照
        await diligenceService.refreshSnapshot(params);
        // 更新排查数据
        await fetchData(company.value.KeyNo, true);
        resetGenReportState();
      } catch (error) {
        console.error(error);
      } finally {
        setRefreshingSnapshot(false);
      }
    };

    const getCompanyTabs = (passedRoute: Route) => {
      const ids = ((passedRoute.query.ids as string) || '').split(',');
      const names = ((passedRoute.query.names as string) || '').split(',');
      return ids.map((id: string, i: number) => {
        return {
          id,
          name: names[i],
        };
      });
    };

    const handleRouteChange = (params, oldParams: any = {}) => {
      if (params.id !== oldParams.id) {
        fetchData(params.id, false, ['dynamicDetails', 'risk-trends'].includes(params.type));
      }
    };

    /**
     * 注入
     */
    provide('snapshotId', snapshotId);

    const handleSearch = (keywords: string) => {
      router?.push({
        name: 'investigation-search',
        query: {
          keyword: keywords,
        },
      });
    };
    const handleSelect = (option) => {
      router
        .replace({
          name: 'supplier-investigate-detail',
          params: {
            type: 'investigation',
            id: option.id,
          },
          query: {
            diligenceId: undefined,
            from: 'investigation',
          },
        })
        .catch(console.error);
    };

    /**
     * 排查动态不显示操作
     */
    const isInvestigationTrends = (type?: string) => {
      if (!type) {
        return false;
      }
      return ['risk-trends', 'dynamicDetails'].includes(type);
    };

    const handleOpenSettingModal = async () => {
      await openSettingDrawer({ id: unref(riskInfo).orgSettingsId });
    };

    const partnerTags = ref([]);
    const getOtherInfo = ({ partnerInfo }) => {
      partnerTags.value = partnerInfo?.labels?.map((v) => ({ ...v, tooltip: '第三方列表自定义标签' })) || [];
    };

    watch(() => route.params, handleRouteChange, {
      deep: true,
      immediate: true,
    });

    const { tc } = useI18n();

    return {
      dimensionDetails,
      riskInfo,
      company,
      loading,
      riskLevel,
      handleRouteChange,
      getCompanyTabs,
      isZeissOrg,
      refreshingSnapshot,
      snapshotDate,
      handleGenerateReport,
      fetchData,
      errorOutOfLimitation,

      refreshSnapshot,
      handleSearch,
      handleSelect,

      isInvestigationTrends,
      handleOpenSettingModal,

      qualifications,
      credits,

      genReportState,

      partnerTags,
      getOtherInfo,

      tc,
    };
  },
  render() {
    const { company, loading, isZeissOrg, $route } = this;
    let routeList: any = [];

    // FIXME: 重构面包屑组件
    switch ($route.query.from) {
      // 准入排查
      case 'investigation':
        routeList = [
          {
            id: 'investigation',
            route: {
              path: '/supplier/investigation',
              query: {
                useCacheQuery: 'false',
              },
            },
            name: '准入排查',
          },
        ];
        break;
      // 少于5的批量排查
      case 'assessment':
        routeList = [{ id: 'batch-investigation', route: '/supplier/batch-investigation', name: '批量排查' }];
        break;
      // 准入排查列表
      case 'list':
        routeList = [
          {
            id: 'investigation',
            route: {
              path: '/supplier/investigation',
              query: {
                useCacheQuery: 'false',
              },
            },
            name: '准入排查',
          },
          {
            id: 'assessment-batch',
            route: {
              // 区分是排查搜索还是公司搜索
              path: `/supplier/${$route.query.type === 'company' ? 'company' : 'investigation'}/search`,
              query: {
                ...omit(this.$route.query, ['from']),
              },
            },
            name: '搜索结果',
          },
        ];
        break;
      case 'record':
        routeList = [
          {
            id: 'investigation-history',
            route: {
              path: '/supplier/investigation-history',
              query: {
                useCacheQuery: 'true',
              },
            },
            name: '排查记录',
          },
        ];
        break;
      case 'statistics':
        routeList = [{ id: 'batch-investigation', route: '/supplier/batch-investigation', name: '批量排查' }];
        break;
      case 'assessment-batch':
        routeList = [
          { id: 'batch-investigation', route: '/supplier/batch-investigation', name: '批量排查' },
          {
            id: 'assessment-batch',
            route: {
              path: this.$route.query.assessmentBatchId
                ? `/supplier/batch-investigation/detail/${this.$route.query.assessmentBatchId}`
                : 'back',
              query: {
                useCacheQuery: 'true',
              },
            },
            name: '批量排查结果',
          },
        ];
        break;
      case 'annual-review':
        routeList = [
          {
            id: 'risk-annual-review',
            route: `/supplier/third-party/annual-review/risk?useCacheQuery=true`,
            name: '风险巡检',
          },
        ];
        break;
      case 'annual-review-batch':
        routeList = [
          {
            id: 'annual-review-record',
            route: `/supplier/third-party/annual-review/record?useCacheQuery=true`,
            name: '风险巡检',
          },
          {
            id: 'annual-review-investigate',
            route: `/supplier/third-party/annual-review/record/${this.$route.query.assessmentBatchId}`,
            name: '巡检结果',
          },
        ];
        break;
      case 'risk-trends':
        routeList = [
          {
            id: 'risk-trends',
            route: {
              path: `/supplier/risk-trends/overview`,
              query: {
                useCacheQuery: 'true',
              },
            },
            name: '排查动态',
          },
        ];
        break;
      default:
        routeList = [];
    }

    const tabs = this.getCompanyTabs($route).map((tab) => ({
      ...tab,
      isActive: tab.id === $route.params.id,
    }));

    const dimensions = this.riskInfo?.details?.dimensionScoreDetails || [];
    // 是否有面包屑
    const hasBreadcrumb = routeList.length > 0;
    const breadcrumbHeight = hasBreadcrumb ? 50 : 0;
    const bodyPaddingTop = breadcrumbHeight > 0 ? breadcrumbHeight - 10 : breadcrumbHeight; // 需要考虑容器中的 paddingTop
    // 布局样式
    const bodyStyle = getBodyStyle({
      paddingTop: `${bodyPaddingTop}px`,
    });
    // 风险排查内容 context
    const riskContentContext: any = {
      props: {
        loading,
        dimensions: this.riskInfo?.details?.dimensionScoreDetails || [],
        company,
        riskLevel: this.riskInfo?.details?.result || 0,
        tabs,
        qualifications: this.qualifications,
        credits: this.credits,
        offset: {
          y: 102,
          x: 190,
        },
        breadcrumbHeight,
        partnerTags: this.partnerTags,
      },
    };

    // 渲染操作按钮
    const actionsRenderer = () => {
      // 重新排查
      const reScanAction = (
        <Tooltip title="点击将刷新页面重新排查">
          <RiskAction
            tid="refresh-snapshot"
            icon="icon-icon_sqq"
            loading={this.refreshingSnapshot}
            v-debounceclick={this.refreshSnapshot}
            spin
            theme="slight"
          >
            {this.tc('Screening time')}: {this.snapshotDate}
          </RiskAction>
        </Tooltip>
      );

      // 下载报告（轮询）
      const downloadReportAction = (
        <GenerateReportAction genReportState={this.genReportState} onGenerateReport={this.handleGenerateReport} permission={[2002]} />
      );
      // 审核跟进
      const followUpAction = (
        <RiskAction
          v-permission={[2003]}
          icon="icon-a-lianji4"
          onClick={async () => {
            this.$track(createTrackEvent(6204, '准入排查详情页', '审核跟进'));
            const needRefresh = await openAuditFollowDrawer({
              ...riskContentContext.props,
              id: this.riskInfo?.id,
            });
            if (needRefresh) {
              await this.fetchData(this.company.KeyNo, false);
            }
            this.$track(createTrackEvent(6204, '准入排查详情页', '审核跟进'));
          }}
        >
          {this.tc('Follow-up')}
        </RiskAction>
      );
      //  添加至
      const watchAction = <RiskWatch onInit={this.getOtherInfo} />;
      return [
        reScanAction, // 重新排查
        downloadReportAction, // 下载报告（轮询）
        followUpAction, // 审核跟进
        watchAction, // 添加至
      ];
    };

    return (
      <div class={styles.container}>
        <HeroicLayout bodyStyle={bodyStyle} loading={loading}>
          {company?.Name && routeList?.length ? (
            <div class="flex justify-between breadcrumb items-center">
              <div>
                <Breadcrumb>
                  {routeList.map((v: any, index: number) => {
                    return (
                      <Breadcrumb.Item key={v.id}>
                        <a
                          class={{ firstItem: index === 0 }}
                          onClick={() => {
                            // 从搜索结果列表进入时，直接返回上一级
                            if (v.id === 'investigateList' && this.$route.query.query === 'list') {
                              this.$router.back();
                              return;
                            }
                            // 上级页面有window.open的行为，返回上一页不可用
                            if (v.route === 'back' || v.route?.path === 'back') {
                              this.$router.back();
                            } else {
                              this.$router.push(v.route);
                            }
                          }}
                        >
                          {index === 0 ? <q-icon type="icon-mianbaoxiefanhui"></q-icon> : null}
                          {v.name}
                        </a>
                      </Breadcrumb.Item>
                    );
                  })}
                  <Breadcrumb.Item style={{ color: '#333' }}>{company?.Name}</Breadcrumb.Item>
                </Breadcrumb>
              </div>
              {['investigation', 'assessment'].includes($route.query.from as string) ? (
                <div class={styles.search}>
                  <CompanySearch
                    size="default"
                    enterText="查一下"
                    onSearch={this.handleSearch}
                    onSelect={this.handleSelect}
                    onRefresh={() => this.refreshSnapshot()}
                  />
                </div>
              ) : null}
            </div>
          ) : null}

          {!loading && company?.KeyNo ? (
            <div>
              {/* 查询多家企业时展示标签 */}
              <RiskTab tabs={tabs} />

              {isZeissOrg ? (
                <RiskContentZeiss {...riskContentContext}>
                  {dimensions.length > 0 && !this.isInvestigationTrends($route.params?.type) ? (
                    <template slot="extra">
                      <RiskAction onClick={this.handleOpenSettingModal} icon="icon-tuisongshezhi1" v-show={this.riskInfo.orgSettingsId}>
                        <q-glossary-info tooltip="当前排查结果关联的模型参数设置">
                          <span slot="trigger">{this.tc('Association Settings')}</span>
                        </q-glossary-info>
                      </RiskAction>
                      {actionsRenderer()}
                    </template>
                  ) : null}
                </RiskContentZeiss>
              ) : (
                <RiskContentDefault {...riskContentContext}>
                  {dimensions.length > 0 && !this.isInvestigationTrends($route.params?.type) ? (
                    <template slot="extra">{actionsRenderer()}</template>
                  ) : null}
                </RiskContentDefault>
              )}
            </div>
          ) : null}
          {/* 用量超限 */}
          <div class={styles.limitation} v-show={this.errorOutOfLimitation.error}>
            <Empty type="search" description={this.errorOutOfLimitation.message} />
          </div>
        </HeroicLayout>
      </div>
    );
  },
});

export default InvestigateDetailPage;
