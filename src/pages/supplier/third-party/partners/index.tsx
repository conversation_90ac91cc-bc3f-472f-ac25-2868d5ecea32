import { cloneDeep, flattenDeep, intersection, isEmpty, omit } from 'lodash';
import { defineComponent, ref, onMounted, computed, unref, getCurrentInstance, watch } from 'vue';
import { Button, message, Checkbox, Space } from 'ant-design-vue';
import moment from 'moment';
import { useRoute } from 'vue-router/composables';

import QCard from '@/components/global/q-card';
import { customer, blackList } from '@/shared/services';
import { stateToQuery } from '@/utils/search-transform/company/state-to-query';
import CommonSearchFilter from '@/components/common/common-search-filter';
import HeroicLayout from '@/shared/layouts/heroic';
import { useMenuStore } from '@/hooks/use-menu-store';
import { useAbility } from '@/libs/plugins/user-ability';
import UserCountStatistics from '@/components/user-count-statistics';
import {
  openPartnersModal,
  openTrans2Black,
  openTagEditModal,
  openGroupEditModal,
} from '@/components/modal/supplier/add-partner-modal/hooks';
import { createTrackEvent, useTrack } from '@/config/tracking-events';
import { useUserStore } from '@/shared/composables/use-user-store';
import { getFilterOptions } from '@/hooks/use-group-label-oprator';
import { syncUsage } from '@/hooks/use-sync-usage-hook';
import DropdownButtonWapper from '@/components/dropdown-button-wrapper';
import { EXPORTITEMS } from '@/config/record.config';
import { useCommonSettingStore } from '@/hooks/use-common-settings';
import { useUnionFilter } from '@/hooks/use-union-filter';
import CommonResult from '@/shared/components/common-result';
import QIcon from '@/components/global/q-icon';
import { useTrackEvent } from '@/hooks/use-track-event';
import ColumnSort from '@/shared/components/column-sort/column-sort';
import { openInnerCompanyWarning } from '@/shared/components/inner-company-warning';
import { useI18n } from '@/shared/composables/use-i18n';

import RecomendCompanys from './widgets/recommend-companys';
import { getFilterGroups, getTableColumns } from './config';
import { openAnalysisChartModal } from './widgets/analysis-chart-modal';
import styles from './partners.page.module.less';
import { useValidateCount } from '@/shared/composables/use-validate-count';

const PartnersPage = defineComponent({
  name: 'PartnersPage',
  setup() {
    const init = ref(true);
    const inst = getCurrentInstance()?.proxy || {};
    const resRef = ref();
    const { isZeiss } = useUserStore();
    const {
      selectRows,
      dataSource,
      selectedIds,
      pagination,
      sortInfo,
      btnDisable,
      columnsOri,
      columns,
      showBatchData,
      selectedItemLabels,
      showBatchSelectAll,
      generateSelectData,
      isFixed,
      dynamicColumns,
    } = useCommonSettingStore({ idKey: 'customerId', key: 'partners', columnData: getTableColumns(isZeiss.value) });
    const industryMap = ref({});
    const showAddModal = ref(false);
    const isShowPartnerGroup = ref(false);
    const track = useTrack();
    const { handleSearchTrack } = useTrackEvent('第三方列表');
    const recordData = ref(null);
    const companyNames = ref([]); // 批量搜索匹配到公司

    const selectAll = ref(false);
    const filters = ref<Record<string, any>>({});
    const query = ref<Record<string, any>>({});

    const route = useRoute();

    const { allTags, getAllTags } = getFilterOptions({
      type: 1,
      table: 'Customer',
    });

    const getParams = computed(() => {
      const payload = filters.value;

      const convertPayload = stateToQuery(payload);
      const filtersQuery = {
        searchKey: payload.keywords,
        ...payload.filters,
      };

      const getStartDateCode = () => {
        if (!filtersQuery.startDateCode) {
          return undefined;
        }
        if (!filtersQuery.startDateCode?.currently) {
          return {
            currently: true,
            flag: 5,
            min: filtersQuery.startDateCode?.min ? moment().year(filtersQuery.startDateCode?.min).startOf('year') : undefined,
            max: filtersQuery.startDateCode?.max ? moment().year(filtersQuery.startDateCode?.max).endOf('year') : undefined,
            unit: 'year',
          };
        }
        return filtersQuery.startDateCode;
      };
      const enterpriseType = filtersQuery?.enterpriseType?.length ? flattenDeep(filtersQuery?.enterpriseType) : undefined;

      const startDateCode = getStartDateCode();
      return {
        searchKey: filtersQuery?.searchKey,
        enterpriseType,
        treasuryType: filtersQuery?.treasuryType,
        region: convertPayload?.filter?.r,
        industry: convertPayload?.filter?.i,
        departmentIds: filtersQuery?.departmentIds?.length > 0 ? filtersQuery?.departmentIds : undefined,
        updateDate: filtersQuery?.sd ? [filtersQuery?.sd] : undefined,
        groupIds: payload.filters?.g || undefined,
        labelIds: filtersQuery?.t,
        operators: payload.filters?.o,
        statusCode: filtersQuery?.statusCode,
        startDateCode: startDateCode ? [startDateCode] : undefined,
        registcapiAmount: filtersQuery?.registcapiAmount,
        reccapamount: filtersQuery?.reccapamount,
        result: filtersQuery?.result,
        companyNames: companyNames.value.length > 0 ? companyNames.value : undefined,
        pageIndex: pagination.current,
        pageSize: pagination.pageSize,
        principals: filtersQuery?.principals,
        scale: filtersQuery?.scale,
        listStatus: filtersQuery?.listStatus?.length === 1 ? filtersQuery?.listStatus[0] : undefined,
      };
    });

    const getIndustryMap = async () => {
      try {
        industryMap.value = await blackList.searchIndustry();
      } catch (error) {
        console.error(error);
      }
    };

    const {
      getUnionOptions,
      optionsMap,
      isLoading: filterLoading,
      groups,
      getGroups,
      clearFilterStash,
    } = useUnionFilter((aggsField) => {
      return customer.aggsSearch({
        query: aggsField === 'group' ? {} : getParams.value,
        aggsField,
      });
    });

    const filterGroups = computed(() => {
      return getFilterGroups({
        enterpriseType: optionsMap.value.enterpriseType,
        econType: optionsMap.value.treasuryType,
        riskLevel: optionsMap.value.riskLevel,
        registration: optionsMap.value.statusCode,
        tags: optionsMap.value.label,
        departList: optionsMap.value.department,
        operators: optionsMap.value.operator,
        principal: optionsMap.value.principal,
        groups: groups.value,
        listStatus: optionsMap.value.listStatus,
        scale: optionsMap.value.scale,
      });
    });

    const showAnalysisChartModal = async () => {
      const res: any = await openAnalysisChartModal({});
      if (!isEmpty(res)) {
        clearFilterStash();
        filters.value = {
          filters: res.filter,
        };
        getUnionOptions({ ...res.config });
      }
    };

    const handleOpenPartnerModal = async (data = {}, options = {}) => {
      if (!allTags.value?.length) {
        await getAllTags(1);
      }
      return openPartnersModal({
        tab: 'single',
        data,
        groups,
        allTags,
        onUpdated: ({ isOld }) => {
          message.success(!isOld ? '添加成功' : '更新成功');
          getGroups();
          clearFilterStash();
          if (!isOld && pagination.current !== 1) {
            pagination.current = 1;
          } else {
            resRef?.value.search();
          }
          if (!isOld) {
            syncUsage('thirdPartyQuantity', 1);
          }
        },
        ...options,
      });
    };

    const getAllIds = async (params?) => {
      const res = await customer.search(
        params || {
          selectAll: true,
          companyNames: companyNames.value,
        }
      );
      selectRows.value = (res.customerIds || []).map((customerId) => {
        return {
          customerId,
        };
      });
      selectedItemLabels.value = res.labels;
    };

    const handleDelete = async (params) => {
      const { ids, key } = params;
      if (!ids.length && key === 'batch') {
        message.warning('请勾选需要移除的企业');
        return;
      }
      await customer.delete(key === 'all' ? { ...omit(getParams.value, ['pageSize', 'pageIndex']), selectAll: true } : { ids });
      message.success('移除成功');
      selectRows.value = [];
      // 兼容bundleusage接口查询缓慢的问题，若后期修复，可删除
      syncUsage('thirdPartyQuantity', -ids.length);
      // 当批量选中且全部删除后，返回默认列表
      if (showBatchData.value && selectAll.value) {
        showBatchData.value = false;
        companyNames.value = [];
      }
      if (pagination.current === 1) {
        resRef.value?.search();
      } else {
        pagination.current = 1;
      }
      selectAll.value = false;
      if (key === 'all') {
        filters.value = {};
      }
      getGroups();
      clearFilterStash();
    };

    const handlerEdit = (record) => {
      handleOpenPartnerModal(cloneDeep(record), {
        tabs: [{ key: 'single', label: '编辑' }],
      });
    };
    const ability = useAbility();
    const { tc } = useI18n();

    const handleCreatePartner = async () => {
      if (!(await ability.check('stock', ['ThirdPartyQuantity']))) {
        return;
      }
      const tabs = [
        {
          label: tc('Add Third Party'),
          key: 'single',
        },
        {
          label: tc('Batch Upload'),
          key: 'bulk',
        },
      ];
      handleOpenPartnerModal(
        {
          groupId: filters.value?.filters?.g?.length === 1 ? filters.value?.filters?.g[0] : -1,
        },
        { tabs }
      );
    };

    const filtersQuery = JSON.parse(route.query?.filters?.toString() || '{}');
    if (!isEmpty(filtersQuery)) {
      filters.value = {
        filters: filtersQuery,
      };
      Object.keys(filtersQuery).forEach((key) => {
        const configList = getFilterGroups().find((v) => v.field === 'filters').children;
        const config = configList.find((v) => v.field === key);
        getUnionOptions(config);
      });
    }

    onMounted(async () => {
      getGroups();
      getIndustryMap();
    });

    const handleExport = async () => {
      try {
        await customer.export(getParams.value);
        message.success('正在导出，稍后可前往任务列表查看进度');
      } catch (error) {
        console.error(error);
      }
    };

    const handleExportByIds = async () => {
      try {
        if (!selectedIds.value.length) {
          message.warning('请选择需要导出的记录');
          return;
        }
        await customer.export({ ids: selectedIds.value });
        message.success('正在导出，稍后可前往任务列表查看进度');
      } catch (error) {
        console.error(error);
      }
    };

    /**
     * 筛选条件变更时，重新执行搜索
     */
    const handleFilterChange = (payload) => {
      // 分组数据发生变化，重置其他筛选项
      if (
        (payload.filters?.g && intersection(payload.filters?.g, filters.value.filters?.g)?.length !== filters.value?.filters?.g?.length) ||
        payload.filters?.g?.length !== filters.value.filters?.g?.length
      ) {
        payload = {
          filters: {
            g: payload.filters?.g,
          },
        };
      }
      filters.value = payload;
      pagination.current = 1;
      clearFilterStash();
    };

    const isFiltersEmpty = computed(() => {
      return isEmpty(filters.value?.filters) && !filters.value?.keywords;
    });

    const { isInRange } = useValidateCount('customer');

    // 更新分组
    const updateGroup = async (groupId) => {
      const { allowed } = await isInRange({
        validationType: 'group',
        groupId,
        id: selectedIds.value,
      });
      if (!allowed) {
        throw new Error('该分组下企业数已超限');
      }
      const params = {
        groupId,
        customerIds: selectedIds.value,
      };
      await customer.updateGroup(params);
      message.success('移动分组成功');
      selectRows.value = [];
      getGroups();
      // 第一页的是参数未发生改变，需要手动去调用搜索
      if (pagination.current === 1) {
        resRef.value?.search();
      } else {
        pagination.current = 1;
      }
    };

    const openGroupModal = () => {
      track(createTrackEvent(6226, '第三方列表', '移动分组'));
      openGroupEditModal({
        groups: unref(groups).map((el) => ({ groupId: el.value, name: el.label, count: el.count })),
        groupType: 1,
        updateGroup,
      });
    };

    // 批量修改标签
    const updateLabels = async (currentTag) => {
      const params = {
        labelIds: currentTag.map((v) => v.labelId),
        customerIds: selectedIds.value,
      };
      await customer.batchLabels(params);
      message.success('添加标签成功！');
      clearFilterStash('label');
      resRef.value?.search();
    };

    const openTagModal = async () => {
      track(createTrackEvent(6226, '第三方列表', '批量修改标签'));
      if (!allTags.value.length) {
        await getAllTags(1);
      }
      openTagEditModal({
        allTags,
        selectedItemLabels: unref(selectedItemLabels),
        labelType: 1,
        ok: updateLabels,
      });
    };

    // 是否是初次进入页面且无数据
    const isFirstImport = computed(() => {
      return pagination?.current === 1 && isEmpty(filters.value) && pagination.total === 0 && !init.value;
    });

    // 更新cache的数据
    const update = ({ type, value }) => {
      const { total, data, pageIndex, pageSize } = value;
      switch (type) {
        case 'init':
          init.value = false;
          dataSource.value = data;
          pagination.current = pageIndex;
          pagination.total = total;
          pagination.pageSize = pageSize;
          break;
        case 'pagination':
          pagination.current = pageIndex;
          pagination.pageSize = pageSize;
          break;
        default:
          Object.assign(sortInfo, value);
      }
    };

    // 是否是初次进入
    const isFirstIn = ref(false);
    const hasRecommend = ref(true);
    // 最开始为true
    watch(init, (newV, oldV) => {
      if (newV !== oldV) {
        isFirstIn.value = pagination?.current === 1 && isEmpty(filters.value) && pagination.total === 0 && !newV;
      }
    });

    return {
      inst,
      resRef,
      query,
      init,
      filterGroups,
      groups,
      industryMap,
      isShowPartnerGroup,
      dataSource,
      showBatchData,
      selectedItemLabels,
      updateLabels,
      pagination,
      showAddModal,
      openTagModal,
      openGroupModal,
      handleDelete,
      selectRows,
      selectedIds,
      getGroups,
      updateGroup,
      handlerEdit,
      getParams,
      filters,
      isFiltersEmpty,
      handleFilterChange,
      handleExport,
      handleExportByIds,
      handleCreatePartner,
      columns,
      sortInfo,
      handleOpenPartnerModal,
      selectAll,
      getAllIds,
      companyNames,
      recordData,
      columnsOri,
      btnDisable,
      generateSelectData,
      showBatchSelectAll,
      getUnionOptions,
      filterLoading,
      clearFilterStash,
      isFixed,
      dynamicColumns,
      isFirstImport,
      update,
      isFirstIn,
      hasRecommend,
      showAnalysisChartModal,
      handleSearchTrack,
      tc,
    };
  },
  render() {
    const { currentTitle } = useMenuStore();
    return (
      <div>
        <HeroicLayout
          loading={this.init}
          innerStyle={
            this.hasRecommend && this.isFirstIn
              ? { minHeight: 'calc(100vh - 270px)', minWidth: '1230px' }
              : {
                  minWidth: '1230px',
                }
          }
        >
          <QCard
            slot="hero"
            title={currentTitle.value}
            bodyStyle={{
              paddingTop: 0,
            }}
          >
            <CommonSearchFilter
              ref={'searchRef'}
              placeholder="请输入企业名称或统一社会信用代码"
              inputWidth={'320px'}
              batchPlaceholder="请输入完整的企业名称，系统会自动为您匹配到相对应企业，不同企业之间请通过换行间隔。\n\n输入样例：\n企查查科技股份有限公司\n苏州客找找网络科技有限公司"
              filterConfig={this.filterGroups}
              defaultValue={this.filters}
              multiSearch={true}
              loading={this.filterLoading}
              onUpdateBatchData={async (names: string[]) => {
                this.companyNames = names;
                this.pagination.current = 1;
                this.showBatchData = true;
              }}
              onChange={(filterData, group) => {
                this.selectRows = [];
                this.handleFilterChange(filterData);
                this.handleSearchTrack(6228, { keyword: filterData.keywords, filter: group.label });
              }}
              onMultiClear={() => {
                this.showBatchData = false;
                this.selectAll = false;
                this.companyNames = [];
                this.selectRows = [];
              }}
              onGetOptions={this.getUnionOptions}
              onReset={() => this.$track(createTrackEvent(6226, '第三方列表', '重置筛选'))}
            />
          </QCard>
          <CommonResult
            ref="resRef"
            rowKey={'customerId'}
            useCache={true}
            needSelect={true}
            showSelectCount={false}
            searchFn={customer.search}
            selectedIds={this.selectedIds}
            showIndex={false}
            isFixed={this.isFixed}
            industryMap={this.industryMap}
            filterParams={this.getParams}
            groups={this.groups}
            scroll={{ x: true, y: 'calc(100vh - 146px - 255px)' }}
            emptyMinHeight={this.isFirstImport && this.isFirstIn ? 'calc(100vh - 505px)' : undefined}
            columns={this.dynamicColumns}
            onUpdateCache={this.update}
            onImport={() => {
              this.handleOpenPartnerModal(
                {},
                {
                  tab: 'bulk',
                }
              );
            }}
            onSelect={(values) => {
              this.generateSelectData(values);
              this.selectAll = this.showBatchData && this.selectRows.length === this.pagination.total;
            }}
            onEdit={(value) => {
              this.handlerEdit(value);
            }}
            onDelete={(ids) => {
              this.handleDelete({ ids, key: 'batch' });
            }}
            onSingleAddData={this.handleOpenPartnerModal}
          >
            {this.showBatchSelectAll && (
              <div style="margin-bottom: 15px;" slot="batchSelect">
                <Checkbox
                  style={{ height: '22px' }}
                  defaultChecked={this.selectAll}
                  indeterminate={this.selectedIds.length > 0 && this.selectedIds.length < this.pagination.total}
                  checked={this.selectAll}
                  onChange={(e) => {
                    this.selectAll = e.target.checked;
                    if (e.target.checked) {
                      this.getAllIds();
                    } else {
                      this.selectRows = [];
                    }
                  }}
                >
                  全部选中
                </Checkbox>
              </div>
            )}

            <ColumnSort
              slot="columnSort"
              class={['sort-icon', this.showBatchSelectAll ? styles.extraPosition : '']}
              colums={this.columnsOri}
              onChange={(data) => {
                this.columnsOri = data;
              }}
              nativeOnClick={() => {
                this.$track(createTrackEvent(6226, '第三方列表', '设置icon'));
              }}
            />
            <div slot="extra">
              <div class={styles.extra}>
                <UserCountStatistics dimension={'thirdPartyQuantity'} style="margin-right: 0;" />
                <div v-permission={[2141]} class={styles.btnChart} onClick={this.showAnalysisChartModal}>
                  <QIcon type="icon-icon_tubiao"></QIcon>
                  {this.tc('Third Party Analysis')}
                </div>
                <Button
                  v-permission={[2039]}
                  v-disable-tip={'请先选择要操作的企业'}
                  v-show={!!this.dataSource.length}
                  disabled={this.btnDisable}
                  v-debounceclick={() => {
                    openTrans2Black({
                      groupId: -1,
                      select: this.selectRows,
                      handleOk: (sameCompanyList, effectedRows) => {
                        this.selectRows = [];
                        openInnerCompanyWarning(sameCompanyList);
                        if (this.pagination.current === 1) {
                          this.resRef?.search();
                        } else {
                          this.pagination.current = 1;
                        }
                        this.getGroups();
                        this.clearFilterStash();
                        syncUsage('thirdPartyQuantity', -effectedRows.length);
                      },
                    });
                    this.$track(createTrackEvent(6226, '第三方列表', '移入内部黑名单'));
                  }}
                >
                  移入内部黑名单
                </Button>
                <DropdownButtonWapper
                  totalCount={this.pagination.total}
                  permissionCode={[2037]}
                  btnText="移除列表"
                  selectIdlength={this.selectRows.length}
                  onConfirm={(key) => {
                    this.handleDelete({ ids: this.selectedIds, key });
                    this.$track(createTrackEvent(6226, '第三方列表', key === 'all' ? '移除全部' : '移除选中'));
                  }}
                />
                <Button
                  v-permission={[2034]}
                  v-disable-tip={'请先选择要操作的企业'}
                  v-show={!!this.dataSource.length}
                  disabled={this.btnDisable}
                  v-debounceclick={this.openGroupModal}
                >
                  移动分组
                </Button>
                {/* <Button>变更负责人</Button> */}
                <DropdownButtonWapper
                  totalCount={this.pagination.total}
                  v-permission={[2140]}
                  btnText="导出列表"
                  needPopConfirm={false}
                  selectIdlength={this.selectedIds.length}
                  menuItems={EXPORTITEMS}
                  onConfirm={(key) => {
                    if (key === 'export') {
                      this.handleExport();
                      this.$track(createTrackEvent(6226, '第三方列表', '全部导出'));
                    } else {
                      this.handleExportByIds();
                      this.$track(createTrackEvent(6226, '第三方列表', '选中导出'));
                    }
                  }}
                />
                <Button
                  v-disable-tip={'请先选择要操作的企业'}
                  v-permission={[2036]}
                  disabled={this.btnDisable}
                  v-debounceclick={this.openTagModal}
                >
                  批量修改标签
                </Button>

                <Button
                  v-permission={[2032]}
                  type={'primary'}
                  icon="plus-circle"
                  onclick={() => {
                    this.handleCreatePartner();
                    this.$track(createTrackEvent(6226, '第三方列表', '新增第三方'));
                  }}
                >
                  {this.tc('Add Third Party')}
                </Button>
              </div>
            </div>
            <div slot="emptyExtra">
              <Space v-permission={[2032]} v-show={this.isFirstImport} style="margin-top: 15px;">
                <Button
                  data-testid="btn-import-single"
                  class={styles.singleAdd}
                  onClick={() => {
                    this.handleOpenPartnerModal();
                    this.$track(createTrackEvent(6226, '第三方列表', '单个添加'));
                  }}
                >
                  单个添加
                </Button>
                <Button
                  data-testid="btn-import-all"
                  type="primary"
                  onClick={() => {
                    this.handleOpenPartnerModal(
                      {},
                      {
                        tab: 'bulk',
                      }
                    );
                    this.$track(createTrackEvent(6226, '第三方列表', '批量导入'));
                  }}
                  class={styles.allImport}
                >
                  批量导入
                </Button>
              </Space>
            </div>
          </CommonResult>
        </HeroicLayout>

        {this.isFirstIn && (
          <RecomendCompanys
            v-show={this.hasRecommend}
            style="margin-top: 15px;"
            onNodata={(data) => {
              this.hasRecommend = !!data;
            }}
            onAddSuccess={() => {
              this.resRef?.search();
            }}
          />
        )}
      </div>
    );
  },
});

export default PartnersPage;
