import { defineComponent, onMounted, reactive, ref } from 'vue';

import QModal from '@/components/global/q-modal';
import { blackList } from '@/shared/services';
import AddBlacklistForm from '@/components/modal/supplier/add-blacklist-modal/widgets/add-blacklist-modal-form';

// 转移黑名单提示框
const TransIntoBlack = defineComponent({
  name: 'TransIntoBlack',
  props: {
    params: {
      type: Object,
      default: () => ({}),
    },
  },
  setup(props, { emit }) {
    const form = ref(null);
    const formData = reactive({
      name: '',
      companyName: '',
      econkind: '',
      companyId: '',
      areacode: '',
      econkindcode: '',
      industry: '',
      province: '',
      reason: '',
      department: '',
      date: '',
      joinDate: '',
      expiredDate: '',
      note: '',
      groupId: props.params.groupId || -1,
      labelIds: '',
      transData: props.params.select || undefined,
    });
    const visible = ref(false);
    const loading = ref(false);

    const onCancel = () => {
      visible.value = false;
    };
    const handleOnOk = () => {
      return new Promise((resolve, reject) => {
        const blacklistFormRef = form.value?.$refs?.WrappedComponent?.form;
        loading.value = true;
        blacklistFormRef.validateFields(async (err, values) => {
          if (err) {
            loading.value = false;
            reject();
            return;
          }
          // ajax
          const params = {
            // department: values.department || null,
            departmentNames: values.departmentNames?.length ? values.departmentNames : undefined,
            comment: values.note || null,
            reason: values.reason || null,
            duration: values.date,
            joinDate: values.joinDate || null,
            expiredDate: values.expiredDate || null,
            groupId: values.groupId || null,
            labelIds: values?.labelIds?.length > 0 ? values.labelIds : undefined,
            customerIds: props.params.select.map((item: any) => item.customerId),
          };

          blackList
            .transParter2Black(params)
            .then((res) => {
              const { sameCompanyList, effectedRows } = res;
              onCancel();
              props.params.handleOk?.(sameCompanyList, effectedRows);
              loading.value = false;
              resolve(true);
              emit('resolve');
            })
            // eslint-disable-next-line @typescript-eslint/no-shadow
            .catch((err) => {
              loading.value = false;
              resolve(true);
            });
        });
      });
    };

    onMounted(() => {
      visible.value = true;
    });

    return {
      form,
      visible,
      formData,
      loading,
      handleOnOk,
      onCancel,
    };
  },
  render() {
    return (
      <QModal
        {...{
          props: {
            onOk: this.handleOnOk,
            visible: this.visible,
            loading: this.loading,
            // wrapClassName: styles.container,
            destroyOnClose: true,
            size: 'medium',
          },
          on: {
            cancel: () => this.onCancel(),
          },
        }}
      >
        <div slot="title" class="flex-between" style="padding-right: 12px;">
          移入黑名单
        </div>
        <AddBlacklistForm ref="form" formData={this.formData} />
      </QModal>
    );
  },
});
export default TransIntoBlack;
